<?php

namespace Modules\RajaGambar\Traits;

use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Config;
use Modules\RajaGambar\Filament\Forms\Components\RajaGambarUpload;

trait PakaiRajaGambar
{
    /**
     * Get RajaGambar configuration
     */
    protected static function getRajaGambarConfig(): array
    {
        return Config::get('rajagambar', []);
    }

    /**
     * Get storage disk from config
     */
    protected static function getStorageDisk(): string
    {
        $config = self::getRajaGambarConfig();
        return $config['storage']['disk'] ?? 'public';
    }

    /**
     * Get base storage path from config
     */
    protected static function getBasePath(): string
    {
        $config = self::getRajaGambarConfig();
        return $config['storage']['path'] ?? 'uploads';
    }

    /**
     * Get URL prefix from config
     */
    protected static function getUrlPrefix(): string
    {
        $config = self::getRajaGambarConfig();
        return $config['storage']['url_prefix'] ?? '/storage';
    }

    /**
     * Get max file size from config
     */
    protected static function getMaxFileSize(): int
    {
        $config = self::getRajaGambarConfig();
        return $config['upload']['max_file_size'] ?? 10240;
    }

    /**
     * Get max files from config
     */
    protected static function getMaxFiles(): int
    {
        $config = self::getRajaGambarConfig();
        return $config['upload']['max_files'] ?? 10;
    }

    /**
     * Get temporary directory from config
     */
    protected static function getTempDirectory(): string
    {
        $config = self::getRajaGambarConfig();
        return $config['upload']['temporary_directory'] ?? 'livewire-tmp';
    }

    /**
     * Get full upload path for directory
     */
    protected static function getFullUploadPath(string $directory): string
    {
        return self::getBasePath() . '/' . $directory;
    }

    /**
     * Configure FileUpload with RajaGambar settings
     */
    public static function configureRajaGambarFileUpload(FileUpload $component, string $directory = 'images', ?string $subdirectory = null): FileUpload
    {
        $fullPath = $subdirectory
            ? self::getFullUploadPath($directory) . '/' . $subdirectory
            : self::getFullUploadPath($directory);

        return $component
            ->disk(self::getStorageDisk())
            ->directory($fullPath)
            ->maxSize(self::getMaxFileSize())
            ->maxFiles(self::getMaxFiles())
            ->acceptedFileTypes(self::getAcceptedFileTypes());
    }

    /**
     * Create configured FileUpload for RajaGambar
     */
    public static function makeRajaGambarFileUpload(string $name, string $directory = 'images', ?string $subdirectory = null): FileUpload
    {
        $component = FileUpload::make($name);
        return self::configureRajaGambarFileUpload($component, $directory, $subdirectory);
    }

    /**
     * Create FileUpload for general images
     */
    public static function makeImageUpload(string $name): FileUpload
    {
        return self::makeRajaGambarFileUpload($name, 'images');
    }

    /**
     * Create FileUpload for gallery images
     */
    public static function makeGalleryUpload(string $name): FileUpload
    {
        return self::makeRajaGambarFileUpload($name, 'gallery');
    }

    /**
     * Create FileUpload for product images
     */
    public static function makeProductUpload(string $name): FileUpload
    {
        return self::makeRajaGambarFileUpload($name, 'products');
    }

    /**
     * Create FileUpload for banner images
     */
    public static function makeBannerUpload(string $name): FileUpload
    {
        return self::makeRajaGambarFileUpload($name, 'banners');
    }

    /**
     * Create FileUpload for avatar images
     */
    public static function makeAvatarUpload(string $name): FileUpload
    {
        return self::makeRajaGambarFileUpload($name, 'avatars')
            ->maxFiles(1)
            ->imageEditor()
            ->circleCropper();
    }

    /**
     * Create FileUpload for thumbnail images
     */
    public static function makeThumbnailUpload(string $name): FileUpload
    {
        return self::makeRajaGambarFileUpload($name, 'thumbnails')
            ->maxFiles(1);
    }

    /**
     * Get accepted file types from config
     */
    protected static function getAcceptedFileTypes(): array
    {
        $config = self::getRajaGambarConfig();
        $mimeTypes = $config['security']['allowed_mime_types'] ?? [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/webp'
        ];
        
        return $mimeTypes;
    }

    /**
     * Get file URL using config
     */
    public static function getFileUrl(string $filePath): string
    {
        return self::getUrlPrefix() . '/' . $filePath;
    }

    /**
     * Create FileUpload with custom directory
     */
    public static function makeCustomDirectoryUpload(string $name, string $directory): FileUpload
    {
        return self::makeRajaGambarFileUpload($name, $directory);
    }

    /**
     * Create multiple FileUpload with different directories
     */
    public static function makeMultiDirectoryUploads(array $fields): array
    {
        $uploads = [];

        foreach ($fields as $name => $directory) {
            $uploads[] = self::makeRajaGambarFileUpload($name, $directory);
        }

        return $uploads;
    }

    /**
     * Create RajaGambarUpload component with directory support
     * This component supports ->directory() method for subdirectories
     */
    public static function makeRajaGambarUpload(string $name): RajaGambarUpload
    {
        return RajaGambarUpload::make($name);
    }

    /**
     * Create RajaGambarUpload for images with directory support
     */
    public static function makeImageUploadWithDirectory(string $name): RajaGambarUpload
    {
        return RajaGambarUpload::make($name)->imagesDirectory();
    }

    /**
     * Create RajaGambarUpload for gallery with directory support
     */
    public static function makeGalleryUploadWithDirectory(string $name): RajaGambarUpload
    {
        return RajaGambarUpload::make($name)->galleryDirectory();
    }

    /**
     * Create RajaGambarUpload for products with directory support
     */
    public static function makeProductUploadWithDirectory(string $name): RajaGambarUpload
    {
        return RajaGambarUpload::make($name)->productsDirectory();
    }

    /**
     * Create RajaGambarUpload for banners with directory support
     */
    public static function makeBannerUploadWithDirectory(string $name): RajaGambarUpload
    {
        return RajaGambarUpload::make($name)->bannersDirectory();
    }

    /**
     * Create RajaGambarUpload for avatars with directory support
     */
    public static function makeAvatarUploadWithDirectory(string $name): RajaGambarUpload
    {
        return RajaGambarUpload::make($name)->avatarsDirectory();
    }
}
