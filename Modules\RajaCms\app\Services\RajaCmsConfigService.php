<?php

namespace Modules\RajaCms\Services;

use Illuminate\Support\Facades\Config;

class RajaCmsConfigService
{
    protected array $config;

    public function __construct()
    {
        $this->config = Config::get('rajacms', []);
    }

    /**
     * Get storage disk name
     */
    public function getStorageDisk(): string
    {
        return $this->config['storage']['disk'] ?? 'public';
    }

    /**
     * Get base storage path
     */
    public function getBasePath(): string
    {
        return $this->config['storage']['base_path'] ?? 'uploads';
    }

    /**
     * Get URL prefix
     */
    public function getUrlPrefix(): string
    {
        return $this->config['storage']['url_prefix'] ?? '/storage';
    }

    /**
     * Get max file size in KB
     */
    public function getMaxFileSize(): int
    {
        return $this->config['upload']['max_file_size'] ?? 10240;
    }

    /**
     * Get max files count
     */
    public function getMaxFiles(): int
    {
        return $this->config['upload']['max_files'] ?? 10;
    }

    /**
     * Get temporary directory
     */
    public function getTempDirectory(): string
    {
        return $this->config['upload']['temporary_directory'] ?? 'livewire-tmp';
    }

    /**
     * Get directory path for specific type
     */
    public function getDirectory(string $type): string
    {
        return $this->config['directories'][$type] ?? $type;
    }

    /**
     * Get full upload path for directory
     */
    public function getFullUploadPath(string $directory): string
    {
        return $this->getBasePath() . '/' . $this->getDirectory($directory);
    }

    /**
     * Get full URL for uploaded file
     */
    public function getFileUrl(string $filePath): string
    {
        return $this->getUrlPrefix() . '/' . $filePath;
    }

    /**
     * Get all directories configuration
     */
    public function getDirectories(): array
    {
        return $this->config['directories'] ?? [];
    }
}
