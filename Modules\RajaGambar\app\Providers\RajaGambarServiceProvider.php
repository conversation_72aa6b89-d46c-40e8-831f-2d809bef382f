<?php

namespace Modules\RajaGambar\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use <PERSON>widart\Modules\Traits\PathNamespace;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Modules\RajaGambar\Services\InterventionImageService;
use Modules\RajaGambar\Services\RajaGambarConfigService;
use Modules\RajaGambar\Services\ThumbnailService;

class RajaGambarServiceProvider extends ServiceProvider
{
    use PathNamespace;

    protected string $name = 'RajaGambar';

    protected string $nameLower = 'rajagambar';

    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerCommands();
        $this->registerCommandSchedules();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->name, 'database/migrations'));
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {
        $this->app->register(EventServiceProvider::class);
        $this->app->register(RouteServiceProvider::class);

        // Register InterventionImageService as singleton
        $this->app->singleton(InterventionImageService::class, function () {
            return new InterventionImageService();
        });

        // Register RajaGambarConfigService as singleton
        $this->app->singleton(RajaGambarConfigService::class);

        // Register ThumbnailService as singleton
        $this->app->singleton(ThumbnailService::class, function ($app) {
            return new ThumbnailService($app->make(InterventionImageService::class));
        });

        // Register service with alias
        $this->app->alias(InterventionImageService::class, 'rajagambar.image');
        $this->app->alias(RajaGambarConfigService::class, 'rajagambar.config');
        $this->app->alias(ThumbnailService::class, 'rajagambar.thumbnail');

        // Register helper functions for RajaGambar
        $this->registerHelpers();
    }

    /**
     * Register helper functions
     */
    protected function registerHelpers(): void
    {
        // Helper functions are auto-loaded via composer.json files section
        // Available helpers:
        // - rajagambar_service() - Get InterventionImageService instance
        // - rajagambar_process() - Process image with options
        // - rajagambar_thumbnail() - Generate thumbnail
        // - rajagambar_info() - Get image information
        // - rajagambar_validate() - Validate image format
        // - rajagambar_thumbnails() - Get ThumbnailService instance
    }

    /**
     * Register commands in the format of Command::class
     */
    protected function registerCommands(): void
    {
        // $this->commands([]);
    }

    /**
     * Register command Schedules.
     */
    protected function registerCommandSchedules(): void
    {
        // $this->app->booted(function () {
        //     $schedule = $this->app->make(Schedule::class);
        //     $schedule->command('inspire')->hourly();
        // });
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/'.$this->nameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->nameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->name, 'lang'), $this->nameLower);
            $this->loadJsonTranslationsFrom(module_path($this->name, 'lang'));
        }
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $relativeConfigPath = config('modules.paths.generator.config.path');
        $configPath = module_path($this->name, $relativeConfigPath);

        if (is_dir($configPath)) {
            $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($configPath));

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $relativePath = str_replace($configPath . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $configKey = $this->nameLower . '.' . str_replace([DIRECTORY_SEPARATOR, '.php'], ['.', ''], $relativePath);
                    $key = ($relativePath === 'config.php') ? $this->nameLower : $configKey;

                    $this->publishes([$file->getPathname() => config_path($relativePath)], 'config');
                    $this->mergeConfigFrom($file->getPathname(), $key);
                }
            }
        }
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/'.$this->nameLower);
        $sourcePath = module_path($this->name, 'resources/views');

        $this->publishes([$sourcePath => $viewPath], ['views', $this->nameLower.'-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->nameLower);

        $componentNamespace = $this->module_namespace($this->name, $this->app_path(config('modules.paths.generator.component-class.path')));
        Blade::componentNamespace($componentNamespace, $this->nameLower);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            InterventionImageService::class,
            RajaGambarConfigService::class,
            ThumbnailService::class,
            'rajagambar.image',
            'rajagambar.config',
            'rajagambar.thumbnail',
        ];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path.'/modules/'.$this->nameLower)) {
                $paths[] = $path.'/modules/'.$this->nameLower;
            }
        }

        return $paths;
    }
}
