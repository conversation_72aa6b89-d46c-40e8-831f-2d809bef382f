<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Forms;

use Modules\RajaCms\Filament\Resources\CmsResource;
use App\Helpers\FileHelper;
use App\Models\KategoriArtikel;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;
use Modules\Rajapicker\Filament\Forms\Components\RajaPickerExt;
use Modules\RajaCms\Filament\Forms\Components\RajaCmsFileUpload;
use Modules\RajaCms\Traits\ConfiguresFileUpload;
use Modules\RajaCms\Services\RajaCmsConfigService;

class InfoKonten
{
    use ConfiguresFileUpload;
    public static function make(): Grid
    {
        return Grid::make('Info Konten')


            ->schema([
                TextInput::make('judul')
                    ->required()
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Set $set, ?string $state) {
                        $slug = Str::slug($state);
                        $uniqueSlug =  self::generateUniqueSlug($slug); // Using the full class path
                        $set('slug', $uniqueSlug);
                    })->columnSpanFull(),
                Select::make('kategori_id')
                    ->options(fn() => KategoriArtikel::get()->pluck('nama', 'id'))
                    ->visible(fn($get) => $get('jenis') == 'ARTIKEL')
                    ->relationship('kategoriArtikel', 'nama')
                    ->createOptionForm([
                        TextInput::make('nama')->label('nama kategori')
                            ->required(),
                    ])->columnSpanFull(),
                TextInput::make('slug')

                    ->label('slug')
                    // ->readOnly()
                    ->columnSpanFull()
                    ->helperText('Di generate otomatis ')
                    ->rules(['required', 'string', 'max:255']),

                Radio::make('status')
                    ->options(function ($get) {
                        $options = [
                            'draft' => 'Draft',
                            'tampil' => 'Tampil',
                        ];

                        // Hanya tampilkan opsi "home" jika jenis == "HALAMAN"
                        if ($get('jenis') === 'HALAMAN') {
                            $options['home'] = 'Jadikan halaman utama';
                        }

                        return $options;
                    })
                    ->default('tampil')
                    ->columnSpan(1)
                    ->live(),



          

                // Opsi 1: Menggunakan Custom Component
                RajaCmsFileUpload::make('gambar')
                    ->label('Gambar Utama')
                    ->cmsDirectory('cms')
                    ->columnSpanFull(),

                // Opsi 2: Menggunakan Trait Method
                self::makeCmsFileUpload('jcol.gambar', 'cms')
                    ->label('Gambar kedua')
                    ->columnSpanFull(),

                // Opsi 3: Menggunakan Service langsung
                FileUpload::make('banner')
                    ->label('Banner')
                    ->disk(app(RajaCmsConfigService::class)->getStorageDisk())
                    ->directory(app(RajaCmsConfigService::class)->getFullUploadPath('banners'))
                    ->maxSize(app(RajaCmsConfigService::class)->getMaxFileSize())
                    ->columnSpanFull(),

 








            ]);
    }

    private static function generateUniqueSlug($slug)
    {
        $baseSlug = $slug;
        $count = 2;

        // Check if the slug already exists in the database
        while (DB::table('cms')->where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $count; // Add a number to the end of the slug
            $count++;
        }

        return $slug; // Return the unique slug
    }
}
