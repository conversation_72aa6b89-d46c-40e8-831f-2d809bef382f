<?php

namespace Modules\RajaCms\Filament\Forms\Components;

use Filament\Forms\Components\FileUpload;
use Modules\RajaCms\Services\RajaCmsConfigService;

class RajaCmsFileUpload extends FileUpload
{
    protected RajaCmsConfigService $configService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->configService = app(RajaCmsConfigService::class);
        
        // Set default disk
        $this->disk($this->configService->getStorageDisk());
        
        // Set default max file size
        $this->maxSize($this->configService->getMaxFileSize());
        
        // Set default max files
        $this->maxFiles($this->configService->getMaxFiles());
    }

    /**
     * Set directory using config service
     */
    public function cmsDirectory(string $type = 'cms'): static
    {
        $fullPath = $this->configService->getFullUploadPath($type);
        return $this->directory($fullPath);
    }

    /**
     * Set directory for articles
     */
    public function articlesDirectory(): static
    {
        return $this->cmsDirectory('articles');
    }

    /**
     * Set directory for pages
     */
    public function pagesDirectory(): static
    {
        return $this->cmsDirectory('pages');
    }

    /**
     * Set directory for banners
     */
    public function bannersDirectory(): static
    {
        return $this->cmsDirectory('banners');
    }

    /**
     * Get file URL using config service
     */
    public function getFileUrl(string $filePath): string
    {
        return $this->configService->getFileUrl($filePath);
    }
}
