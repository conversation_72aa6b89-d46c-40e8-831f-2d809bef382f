<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Forms;

use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

class SlideshowForm
{
   public static function make(): Section
   {
      return Section::make('SLIDE SHOW')

         ->collapsible()
         // ->collapsed()
         ->schema([
            Repeater::make('jcol.slideshow')
               ->visible(fn($get) => $get('status') == 'home')
               ->schema([
                  TextInput::make('judul')->label('Judul'),
                  Textarea::make('isi')->label('konten')->rows(4),

 

                   

               ])
               ->grid(2)
               ->addActionLabel('Tambah slide show'),

         ]);
   }
}
