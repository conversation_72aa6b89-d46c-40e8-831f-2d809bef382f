<?php

if (!function_exists('rajagambar_service')) {
    /**
     * Get InterventionImageService instance
     */
    function rajagambar_service(): \Modules\RajaGambar\Services\InterventionImageService
    {
        return app('rajagambar.image');
    }
}

if (!function_exists('rajagambar_thumbnail_service')) {
    /**
     * Get ThumbnailService instance
     */
    function rajagambar_thumbnail_service(): \Modules\RajaGambar\Services\ThumbnailService
    {
        return app('rajagambar.thumbnail');
    }
}

if (!function_exists('rajagambar_config')) {
    /**
     * Get RajaGambarConfigService instance
     */
    function rajagambar_config(): \Modules\RajaGambar\Services\RajaGambarConfigService
    {
        return app('rajagambar.config');
    }
}

if (!function_exists('rajagambar_process')) {
    /**
     * Process image with options
     */
    function rajagambar_process(string $inputPath, string $outputPath, array $options = []): bool
    {
        return rajagambar_service()->processImage($inputPath, $outputPath, $options);
    }
}

if (!function_exists('rajagambar_thumbnail')) {
    /**
     * Generate single thumbnail
     */
    function rajagambar_thumbnail(string $inputPath, string $outputPath, int $width, int $height, array $options = []): bool
    {
        return rajagambar_service()->createThumbnail($inputPath, $outputPath, $width, $height, $options);
    }
}

if (!function_exists('rajagambar_percentage_thumbnail')) {
    /**
     * Generate percentage-based thumbnail
     */
    function rajagambar_percentage_thumbnail(string $inputPath, string $outputPath, int $percentage, array $options = []): bool
    {
        return rajagambar_service()->createPercentageThumbnail($inputPath, $outputPath, $percentage, $options);
    }
}

if (!function_exists('rajagambar_generate_thumbnails')) {
    /**
     * Generate all configured thumbnails for an image
     */
    function rajagambar_generate_thumbnails(string $originalPath, string $baseDirectory = ''): array
    {
        return rajagambar_thumbnail_service()->generateThumbnails($originalPath, $baseDirectory);
    }
}

if (!function_exists('rajagambar_delete_thumbnails')) {
    /**
     * Delete all thumbnails for a file
     */
    function rajagambar_delete_thumbnails(string $originalPath, string $baseDirectory = ''): bool
    {
        return rajagambar_thumbnail_service()->deleteThumbnails($originalPath, $baseDirectory);
    }
}

if (!function_exists('rajagambar_thumbnail_url')) {
    /**
     * Get thumbnail URL
     */
    function rajagambar_thumbnail_url(string $originalPath, string $sizeName, string $baseDirectory = ''): ?string
    {
        return rajagambar_thumbnail_service()->getThumbnailUrl($originalPath, $sizeName, $baseDirectory);
    }
}

if (!function_exists('rajagambar_thumbnail_exists')) {
    /**
     * Check if thumbnail exists
     */
    function rajagambar_thumbnail_exists(string $originalPath, string $sizeName, string $baseDirectory = ''): bool
    {
        return rajagambar_thumbnail_service()->thumbnailExists($originalPath, $sizeName, $baseDirectory);
    }
}

if (!function_exists('rajagambar_info')) {
    /**
     * Get image information
     */
    function rajagambar_info(string $path): array
    {
        return rajagambar_service()->getImageInfo($path);
    }
}

if (!function_exists('rajagambar_validate')) {
    /**
     * Validate if file is a supported image format
     */
    function rajagambar_validate(string $path): bool
    {
        return rajagambar_service()->isValidImageFormat($path);
    }
}

if (!function_exists('rajagambar_convert_to_webp')) {
    /**
     * Convert image to WebP format
     */
    function rajagambar_convert_to_webp(string $inputPath, string $outputPath, ?int $quality = null): bool
    {
        try {
            $service = rajagambar_service();
            $image = $service->loadImage($inputPath);
            $image = $service->convertToWebp($image, $quality);
            $image->save($outputPath);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

if (!function_exists('rajagambar_resize')) {
    /**
     * Resize image
     */
    function rajagambar_resize(string $inputPath, string $outputPath, ?int $width = null, ?int $height = null, bool $aspectRatio = true): bool
    {
        try {
            $service = rajagambar_service();
            $image = $service->loadImage($inputPath);
            $image = $service->resize($image, $width, $height, $aspectRatio);
            $image->save($outputPath);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

if (!function_exists('rajagambar_crop')) {
    /**
     * Crop image
     */
    function rajagambar_crop(string $inputPath, string $outputPath, int $width, int $height, ?int $x = null, ?int $y = null): bool
    {
        try {
            $service = rajagambar_service();
            $image = $service->loadImage($inputPath);
            $image = $service->crop($image, $width, $height, $x, $y);
            $image->save($outputPath);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

if (!function_exists('rajagambar_get_thumbnail_sizes')) {
    /**
     * Get available thumbnail sizes from config
     */
    function rajagambar_get_thumbnail_sizes(): array
    {
        return rajagambar_thumbnail_service()->getThumbnailSizes();
    }
}

if (!function_exists('rajagambar_is_webp_enabled')) {
    /**
     * Check if WebP conversion is enabled
     */
    function rajagambar_is_webp_enabled(): bool
    {
        return rajagambar_service()->isWebpEnabled();
    }
}

if (!function_exists('rajagambar_get_storage_path')) {
    /**
     * Get storage path for uploads
     */
    function rajagambar_get_storage_path(): string
    {
        return rajagambar_service()->getStoragePath();
    }
}

if (!function_exists('rajagambar_get_storage_disk')) {
    /**
     * Get storage disk
     */
    function rajagambar_get_storage_disk(): string
    {
        return rajagambar_service()->getStorageDisk();
    }
}
