<?php
// VERSI 6
namespace Modules\RajaForm\Filament\Forms\Components;

use Filament\Forms\Components\Component;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Get;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Closure;
use Filament\Forms\Components\View;
use Modules\RajaGambar\Filament\Forms\Components\RajaGambarUpload;

/**
 * Class untuk membuat field dari data JSON
 */
class FieldBuilder6
{
    /**
     * Membuat field dari data JSON
     *
     * @param string|array|Closure $data Data form (JSON, array, atau closure)
     * @param string $namaHiddenField Nama hidden field
     * @param bool $gunakanHidden Aktifkan hidden field
     * @param string $pesanKosong Pesan jika data kosong
     * @return array|Closure
     */
    public static function buatFields(
        string|array|Closure $data,
        string $namaHiddenField = 'info_form',
        bool $gunakanHidden = true,
        string $pesanKosong = 'Tidak ada data field'
    ): array|Closure // Ubah tipe pengembalian menjadi array|Closure
    {
        // Tangani jika data adalah closure
        if ($data instanceof Closure) {
            // Kembalikan fungsi yang akan dievaluasi saat form dirender
            return function (Get $get) use ($data, $namaHiddenField, $gunakanHidden, $pesanKosong) {
                $resolvedData = $data($get);
                return static::buatFieldsInternal($resolvedData, $namaHiddenField, $gunakanHidden, $pesanKosong);
            };
        }

        // Gunakan metode internal yang mengembalikan array
        return static::buatFieldsInternal($data, $namaHiddenField, $gunakanHidden, $pesanKosong);
    }

    /**
     * Implementasi internal dari buatFields yang selalu mengembalikan array
     *
     * @param string|array|null $data Data form (JSON atau array)
     * @param string $namaHiddenField Nama hidden field
     * @param bool $gunakanHidden Aktifkan hidden field
     * @param string $pesanKosong Pesan jika data kosong
     * @return array
     */
    protected static function buatFieldsInternal(
        string|array|null $data,
        string $namaHiddenField = 'info_form',
        bool $gunakanHidden = true,
        string $pesanKosong = 'Tidak ada data field'
    ): array {
        // Ambil data field dari JSON/array
        $fields = static::getFieldsData($data);

        // Cek apakah data kosong
        if (static::isDataKosong($fields)) {
            // Jika kosong, kembalikan placeholder dengan pesan
            return [
                Placeholder::make('pesan_kosong')
                    ->content($pesanKosong)
                    ->extraAttributes(['class' => 'text-gray-500 italic'])
            ];
        }

        // Array untuk hasil komponen
        $hasil = [];

        // Simpan nama field untuk hidden field
        $namaField = [];

        // Proses setiap field
        foreach ($fields as $field) {
            // Skip jika tidak lengkap
            if (! isset($field['type']) || ! isset($field['name'])) {
                continue;
            }

            // Tambahkan nama ke daftar
            $namaField[] = $field['name'];

            // Tambahkan komponen ke hasil
            $komponen = static::buatKomponen($field);
            if ($komponen) {
                $hasil[] = $komponen;
            }
        }

        // Tambahkan hidden field jika diperlukan
        if ($gunakanHidden && ! empty($namaField)) {
            $hasil[] = static::buatHiddenField($namaField, $namaHiddenField);
        }

        return $hasil;
    }

    /**
     * Memeriksa apakah data kosong
     */
    protected static function isDataKosong(array $fields): bool
    {
        // Jika array kosong
        if (empty($fields)) {
            return true;
        }

        // Jika array hanya berisi satu item kosong
        if (count($fields) === 1 && empty($fields[0])) {
            return true;
        }

        // Jika semua item tidak memiliki tipe dan nama
        $valid = false;
        foreach ($fields as $field) {
            if (isset($field['type']) && isset($field['name'])) {
                $valid = true;
                break;
            }
        }

        return ! $valid;
    }

    /**
     * Membuat komponen untuk field
     */
    public static function buatKomponen(array $field): ?Component
    {
        // Ambil tipe dan nama field
        $tipe = strtolower($field['type'] ?? '');
        $nama = $field['name'] ?? '';

        // Pastikan nama tidak kosong
        if (empty($nama)) {
            return null;
        }

        // Buat komponen sesuai tipe
        $komponen = null;
        switch ($tipe) {
            case 'textinput':
                $komponen = static::buatTextInput($field);
                break;
            case 'textarea':
                $komponen = static::buatTextarea($field);
                break;
            case 'select':
                $komponen = static::buatSelect($field);
                break;
            case 'radio':
                $komponen = static::buatRadio($field);
                break;
            case 'placeholder':
                $komponen = static::buatPlaceholder($field);
                break;
            case 'viewfield':
                $komponen = static::buatViewField($field);
                break;
            case 'fileupload':
                $komponen = static::buatFileUpload($field);
                break;
            case 'keyvalue':
                $komponen = static::buatKeyValue($field);
                break;
            case 'tagsinput':
                $komponen = static::buatTagsInput($field);
                break;
            case 'rajapicker':
                $komponen = static::buatRajaPicker($field);
                break;
            default:
                return null;
        }

        // Terapkan columnSpan jika ada
        if ($komponen) {
            $columnSpan = $field['columnspan'] ?? 1;
            $komponen->columnSpan($columnSpan);
        }

        return $komponen;
    }

    /**
     * Membuat TextInput
     */
    protected static function buatTextInput(array $field): TextInput
    {
        // Ambil nama dan label
        $nama = $field['name'];

        // Buat komponen dasar
        $komponen = TextInput::make($nama);

        // Handle label (false, string, atau default)
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } else {
                $komponen->label($field['label']);
            }
        } else {
            $komponen->label(Str::title(str_replace('_', ' ', $nama)));
        }

        // Tambahkan properti tambahan
        if (! empty($field['tooltip'])) {
            $komponen->hintIcon('heroicon-m-question-mark-circle', tooltip: $field['tooltip']);
        }

        if (! empty($field['mask'])) {
            $komponen->mask($field['mask']);
        }

        if (! empty($field['placeholder'])) {
            $komponen->placeholder($field['placeholder']);
        }

        // Tambahkan prefix dan suffix jika ada
        if (! empty($field['prefix'])) {
            $komponen->prefix($field['prefix']);
        }

        if (! empty($field['suffix'])) {
            $komponen->suffix($field['suffix']);
        }

        // Tambahkan numerik jika diperlukan
        if (isset($field['is_numeric']) && $field['is_numeric'] === true) {
            $komponen->numeric();
        }

        // Tambahkan property readonly dan disabled
        if (isset($field['readonly']) && $field['readonly'] === true) {
            $komponen->readonly();
        }

        if (isset($field['disabled']) && $field['disabled'] === true) {
            $komponen->disabled();
        }

        if (isset($field['default'])) {
            $komponen->default($field['default']);
        }

        return $komponen;
    }

    /**
     * Membuat Textarea
     */
    protected static function buatTextarea(array $field): Textarea
    {
        // Ambil nama dan label
        $nama = $field['name'];

        // Buat komponen dasar
        $komponen = Textarea::make($nama);

        // Handle label (false, string, atau default)
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } else {
                $komponen->label($field['label']);
            }
        } else {
            $komponen->label(Str::title(str_replace('_', ' ', $nama)));
        }

        // Tambahkan properti tambahan
        if (! empty($field['tooltip'])) {
            $komponen->hintIcon('heroicon-m-question-mark-circle', tooltip: $field['tooltip']);
        }

        if (! empty($field['placeholder'])) {
            $komponen->placeholder($field['placeholder']);
        }

        // Tambahkan property khusus textarea
        if (isset($field['lebar'])) {
            $komponen->cols((int)$field['lebar']);
        }

        if (isset($field['tinggi'])) {
            $komponen->rows((int)$field['tinggi']);
        }

        if (isset($field['autosize']) && $field['autosize'] === true) {
            $komponen->autosize();
        }

        // Tambahkan property readonly dan disabled
        if (isset($field['readonly']) && $field['readonly'] === true) {
            $komponen->readonly();
        }

        if (isset($field['disabled']) && $field['disabled'] === true) {
            $komponen->disabled();
        }

        if (isset($field['default'])) {
            $komponen->default($field['default']);
        }

        return $komponen;
    }

    /**
     * Membuat Select
     */
    protected static function buatSelect(array $field): Select
    {
        // Ambil nama dan label
        $nama = $field['name'];

        // Buat komponen dasar
        $komponen = Select::make($nama);

        // Handle label (false, string, atau default)
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } else {
                $komponen->label($field['label']);
            }
        } else {
            $komponen->label(Str::title(str_replace('_', ' ', $nama)));
        }

        // Tambahkan options
        if (! empty($field['options'])) {
            $options = static::parseOptions($field['options']);
            $komponen->options($options);
        }

        // Tambahkan properti tambahan
        if (! empty($field['tooltip'])) {
            $komponen->hintIcon('heroicon-m-question-mark-circle', tooltip: $field['tooltip']);
        }

        if (! empty($field['placeholder'])) {
            $komponen->placeholder($field['placeholder']);
        }

        // Tambahkan prefix dan suffix jika ada
        if (! empty($field['prefix'])) {
            $komponen->prefix($field['prefix']);
        }

        if (! empty($field['suffix'])) {
            $komponen->suffix($field['suffix']);
        }

        // Tambahkan property readonly dan disabled
        if (isset($field['readonly']) && $field['readonly'] === true) {
            $komponen->disabled();  // Select tidak punya readonly, jadi gunakan disabled
        }

        if (isset($field['disabled']) && $field['disabled'] === true) {
            $komponen->disabled();
        }

        if (isset($field['default'])) {
            $komponen->default($field['default']);
        }

        return $komponen;
    }

    protected static function buatRadio(array $field): Radio
    {
        // Ambil nama dan label
        $nama = $field['name'];

        // Buat komponen dasar
        $komponen = Radio::make($nama);

        // Handle label (false, string, atau default)
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } else {
                $komponen->label($field['label']);
            }
        } else {
            $komponen->label(Str::title(str_replace('_', ' ', $nama)));
        }

        // Tambahkan options
        if (! empty($field['options'])) {
            $options = static::parseOptions($field['options']);
            $komponen->options($options);
        }

        // Tambahkan properti tambahan
        if (! empty($field['tooltip'])) {
            $komponen->hintIcon('heroicon-m-question-mark-circle', tooltip: $field['tooltip']);
        }

        // Tambahkan property readonly dan disabled
        if (isset($field['readonly']) && $field['readonly'] === true) {
            $komponen->disabled();  // Radio tidak punya readonly, jadi gunakan disabled
        }

        if (isset($field['disabled']) && $field['disabled'] === true) {
            $komponen->disabled();
        }

        if (isset($field['default'])) {
            $komponen->default($field['default']);
        }

        if (isset($field['inline']) && $field['inline'] === true) {
            $komponen->inline();
        }

        return $komponen;
    }

    /**
     * Membuat Placeholder
     */
    protected static function buatPlaceholder(array $field): Placeholder
    {
        // Ambil nama dan label
        $nama = $field['name'];
        $default = $field['default'] ?? '';

        // Buat komponen dasar
        $komponen = Placeholder::make($nama);

        // Handle label
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } elseif ($field['label'] !== 'auto') {
                $komponen->label($field['label']);
            } else {
                $komponen->label(Str::title(str_replace('_', ' ', $nama)));
            }
        }

        // Set content dengan HtmlString jika perlu
        $komponen->content(new HtmlString($default));

        // Tambahkan tooltip jika ada
        if (!empty($field['tooltip'])) {
            $komponen->helperText($field['tooltip']);
        }

        return $komponen;
    }

    protected static function buatHiddenField(array $namaField, string $namaHiddenField): Hidden
    {
        return Hidden::make($namaHiddenField)
            ->dehydrateStateUsing(function ($state, $get) use ($namaField) {
                // Kumpulkan nilai dari semua field
                $values = [];
                foreach ($namaField as $field) {
                    $values[$field] = $get($field);
                }

                return json_encode($values, JSON_PRETTY_PRINT);
            });
    }

    protected static function buatViewField(array $field): View
{
    // Ambil nama
    $nama = $field['name'];

    // Buat komponen dasar
    $komponen = View::make($nama);

    // Set view
    if (!empty($field['view'])) {
        $komponen->view($field['view']);
    }

    // Set viewData jika ada
    if (!empty($field['data']) && is_array($field['data'])) {
        $komponen->viewData($field['data']);
    }

    // Handle label (false, string, atau default)
    if (isset($field['label'])) {
        if ($field['label'] === false) {
            $komponen->label(false);
        } else {
            $komponen->label($field['label']);
        }
    } else {
        $komponen->label(Str::title(str_replace('_', ' ', $nama)));
    }

    return $komponen;
}

    /**
     * Membuat FileUpload
     */
    protected static function buatFileUpload(array $field): FileUpload
    {
        // Ambil nama dan label
        $nama = $field['name'];

        // Buat komponen dasar
        $komponen = FileUpload::make($nama);

        // Handle label (false, string, atau default)
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } else {
                $komponen->label($field['label']);
            }
        } else {
            $komponen->label(Str::title(str_replace('_', ' ', $nama)));
        }

        // Tambahkan properti tambahan
        if (!empty($field['tooltip'])) {
            $komponen->hintIcon('heroicon-m-question-mark-circle', tooltip: $field['tooltip']);
        }

        // Set directory jika ada
        if (!empty($field['directory'])) {
            $komponen->directory($field['directory']);
        }

        // Set image editor jika diaktifkan
        if (isset($field['editor']) && $field['editor'] === true) {
            $komponen->imageEditor();
        }

        // Set resize dimensions jika ada
        if (!empty($field['resizewidth']) || !empty($field['resizeheight'])) {
            $width = !empty($field['resizewidth']) ? (int) $field['resizewidth'] : null;
            $height = !empty($field['resizeheight']) ? (int) $field['resizeheight'] : null;

            if ($width && $height) {
                $komponen->resize($width, $height);
            } elseif ($width) {
                $komponen->resize($width);
            }
        }

        // Set file prefix jika ada (menggunakan storeFileNamesIn untuk menyimpan nama asli)
        if (!empty($field['file_prefix'])) {
            $komponen->storeFileNamesIn($nama . '_original_name');
        }

        // Tambahkan property disabled
        if (isset($field['disabled']) && $field['disabled'] === true) {
            $komponen->disabled();
        }

        if (isset($field['default'])) {
            $komponen->default($field['default']);
        }

        return $komponen;
    }

    /**
     * Membuat KeyValue
     */
    protected static function buatKeyValue(array $field): KeyValue
    {
        // Ambil nama dan label
        $nama = $field['name'];

        // Buat komponen dasar
        $komponen = KeyValue::make($nama);

        // Handle label (false, string, atau default)
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } else {
                $komponen->label($field['label']);
            }
        } else {
            $komponen->label(Str::title(str_replace('_', ' ', $nama)));
        }

        // Tambahkan properti tambahan
        if (!empty($field['tooltip'])) {
            $komponen->hintIcon('heroicon-m-question-mark-circle', tooltip: $field['tooltip']);
        }

        // Kustomisasi label key
        if (!empty($field['key_label'])) {
            $komponen->keyLabel($field['key_label']);
        }

        // Kustomisasi label value
        if (!empty($field['value_label'])) {
            $komponen->valueLabel($field['value_label']);
        }

        // Kustomisasi placeholder key
        if (!empty($field['key_placeholder'])) {
            $komponen->keyPlaceholder($field['key_placeholder']);
        }

        // Kustomisasi placeholder value
        if (!empty($field['value_placeholder'])) {
            $komponen->valuePlaceholder($field['value_placeholder']);
        }

        // Kustomisasi label tombol add
        if (!empty($field['add_action_label'])) {
            $komponen->addActionLabel($field['add_action_label']);
        }

        // Pengaturan addable
        if (isset($field['addable']) && $field['addable'] === false) {
            $komponen->addable(false);
        }

        // Pengaturan deletable
        if (isset($field['deletable']) && $field['deletable'] === false) {
            $komponen->deletable(false);
        }

        // Pengaturan editable keys
        if (isset($field['editable_keys']) && $field['editable_keys'] === false) {
            $komponen->editableKeys(false);
        }

        // Pengaturan editable values
        if (isset($field['editable_values']) && $field['editable_values'] === false) {
            $komponen->editableValues(false);
        }

        // Pengaturan reorderable
        if (isset($field['reorderable']) && $field['reorderable'] === true) {
            $komponen->reorderable();
        }

        // Tambahkan property disabled
        if (isset($field['disabled']) && $field['disabled'] === true) {
            $komponen->disabled();
        }

        if (isset($field['default'])) {
            $komponen->default($field['default']);
        }

        return $komponen;
    }

    /**
     * Membuat TagsInput
     */
    protected static function buatTagsInput(array $field): TagsInput
    {
        // Ambil nama dan label
        $nama = $field['name'];

        // Buat komponen dasar
        $komponen = TagsInput::make($nama);

        // Handle label (false, string, atau default)
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } else {
                $komponen->label($field['label']);
            }
        } else {
            $komponen->label(Str::title(str_replace('_', ' ', $nama)));
        }

        // Tambahkan properti tambahan
        if (!empty($field['tooltip'])) {
            $komponen->hintIcon('heroicon-m-question-mark-circle', tooltip: $field['tooltip']);
        }

        if (!empty($field['placeholder'])) {
            $komponen->placeholder($field['placeholder']);
        }

        // Pengaturan separator
        if (!empty($field['separator'])) {
            $komponen->separator($field['separator']);
        }

        // Pengaturan suggestions
        if (!empty($field['suggestions'])) {
            if (is_array($field['suggestions'])) {
                $komponen->suggestions($field['suggestions']);
            } else {
                // Jika string, parse seperti options
                $suggestions = static::parseOptions($field['suggestions']);
                $komponen->suggestions(array_keys($suggestions));
            }
        }

        // Pengaturan split keys
        if (!empty($field['split_keys'])) {
            if (is_array($field['split_keys'])) {
                $komponen->splitKeys($field['split_keys']);
            } else {
                // Jika string, split dengan koma
                $splitKeys = array_map('trim', explode(',', $field['split_keys']));
                $komponen->splitKeys($splitKeys);
            }
        }

        // Pengaturan tag prefix
        if (!empty($field['tag_prefix'])) {
            $komponen->tagPrefix($field['tag_prefix']);
        }

        // Pengaturan tag suffix
        if (!empty($field['tag_suffix'])) {
            $komponen->tagSuffix($field['tag_suffix']);
        }

        // Pengaturan reorderable
        if (isset($field['reorderable']) && $field['reorderable'] === true) {
            $komponen->reorderable();
        }

        // Pengaturan color
        if (!empty($field['color'])) {
            $komponen->color($field['color']);
        }

        // Pengaturan nested recursive rules
        if (!empty($field['nested_rules']) && is_array($field['nested_rules'])) {
            $komponen->nestedRecursiveRules($field['nested_rules']);
        }

        // Tambahkan property disabled
        if (isset($field['disabled']) && $field['disabled'] === true) {
            $komponen->disabled();
        }

        if (isset($field['default'])) {
            $komponen->default($field['default']);
        }

        return $komponen;
    }

    /**
     * Membuat RajaPicker
     */
    protected static function buatRajaPicker(array $field): RajaGambarUpload
    {
        // Ambil nama dan label
        $nama = $field['name'];

        // Buat komponen dasar
        $komponen = RajaGambarUpload::make($nama);

        // Handle label (false, string, atau default)
        if (isset($field['label'])) {
            if ($field['label'] === false) {
                $komponen->label(false);
            } else {
                $komponen->label($field['label']);
            }
        } else {
            $komponen->label(Str::title(str_replace('_', ' ', $nama)));
        }

        // Tambahkan properti tambahan
        if (!empty($field['tooltip'])) {
            $komponen->hintIcon('heroicon-m-question-mark-circle', tooltip: $field['tooltip']);
        }

        if (!empty($field['placeholder'])) {
            $komponen->placeholder($field['placeholder']);
        }

        // Properti khusus RajaGambarUpload
        if (!empty($field['collection'])) {
            $komponen->collection($field['collection']);
        }

        if (isset($field['multiple']) && $field['multiple'] === true) {
            $komponen->multipleImages();
        } else {
            $komponen->singleImage();
        }

        if (isset($field['max_files']) && is_numeric($field['max_files'])) {
            $komponen->maxFiles((int)$field['max_files']);
        }

        if (!empty($field['accepted_types']) && is_array($field['accepted_types'])) {
            $komponen->acceptedFileTypes($field['accepted_types']);
        }

        // Set directory berdasarkan type
        if (!empty($field['directory_type'])) {
            switch ($field['directory_type']) {
                case 'gallery':
                    $komponen->galleryDirectory();
                    break;
                case 'banner':
                    $komponen->bannersDirectory();
                    break;
                case 'avatar':
                    $komponen->avatarsDirectory();
                    break;
                case 'product':
                    $komponen->productsDirectory();
                    break;
                default:
                    $komponen->imagesDirectory();
            }
        } else {
            $komponen->imagesDirectory();
        }

        // Tambahkan property disabled
        if (isset($field['disabled']) && $field['disabled'] === true) {
            $komponen->disabled();
        }

        if (isset($field['default'])) {
            $komponen->default($field['default']);
        }

        return $komponen;
    }

    protected static function getFieldsData(string|array|null $data): array
    {
        // Jika data kosong
        if (empty($data)) {
            return [];
        }

        // Jika data null
        if ($data === null) {
            return [];
        }

        // Jika data sudah array
        if (is_array($data)) {
            return static::normalizeArrayData($data);
        }

        // Jika data string JSON
        try {
            // Bersihkan string
            $dataString = trim($data);

            // Jika string kosong
            if (empty($dataString)) {
                return [];
            }

            // Parse JSON
            $parsed = json_decode($dataString, true);

            // Jika gagal parse
            if (json_last_error() !== JSON_ERROR_NONE) {
                return [];
            }

            return static::normalizeArrayData($parsed ?: []);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Normalkan struktur array data
     */
    protected static function normalizeArrayData(array $data): array
    {
        // Jika data kosong
        if (empty($data)) {
            return [];
        }

        // Cek apakah format sudah benar
        $firstItem = reset($data);
        if (is_array($firstItem) && isset($firstItem['type'])) {
            return $data;
        }

        // Normalkan struktur
        $fields = [];

        foreach ($data as $item) {
            if (is_array($item) && ! isset($item['type'])) {
                // Container
                foreach ($item as $field) {
                    if (is_array($field) && isset($field['type'])) {
                        $fields[] = $field;
                    }
                }
            } elseif (is_array($item) && isset($item['type'])) {
                // Field langsung
                $fields[] = $item;
            }
        }

        return $fields;
    }

    /**
     * Parse options untuk Select dan Radio
     */
    protected static function parseOptions(string|array $options): array
    {
        if (is_array($options)) {
            return $options;
        }

        $hasil = [];
        $daftar = explode(',', $options);

        foreach ($daftar as $option) {
            $option = trim($option);
            $hasil[$option] = Str::title($option);
        }

        return $hasil;
    }
}
