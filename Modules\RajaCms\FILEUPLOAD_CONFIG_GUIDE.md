# Panduan FileUpload dengan Config Path di RajaCms

## Overview

Untuk membuat FileUpload mengikuti aturan path dari config.php (`/storage/uploads/{->directory()}`), tersedia 3 pendekatan:

1. **Trait PakaiRajaGambar** (Recommended)
2. **Custom Component RajaCmsFileUpload**
3. **Service RajaCmsConfigService**

## 1. Menggunakan Trait PakaiRajaGambar (Recommended)

### Setup
```php
use Modules\RajaCms\Traits\PakaiRajaGambar;

class InfoKonten
{
    use PakaiRajaGambar;
    
    public static function make(): Grid
    {
        return Grid::make('Info Konten')->schema([
            // FileUpload components here
        ]);
    }
}
```

### Penggunaan
```php
// Basic CMS upload
self::makeCmsFileUpload('gambar', 'cms')
    ->label('<PERSON>ambar <PERSON>')
    ->columnSpanFull(),

// Article upload
self::makeArticleFileUpload('featured_image')
    ->label('Gambar Artikel')
    ->columnSpanFull(),

// Page upload
self::makePageFileUpload('banner')
    ->label('Banner Halaman')
    ->columnSpanFull(),

// Banner upload
self::makeBannerFileUpload('hero_image')
    ->label('Hero Banner')
    ->columnSpanFull(),
```

### Method yang Tersedia
- `makeCmsFileUpload($name, $directory = 'cms')` - Upload umum CMS
- `makeArticleFileUpload($name)` - Upload untuk artikel
- `makePageFileUpload($name)` - Upload untuk halaman
- `makeBannerFileUpload($name)` - Upload untuk banner

## 2. Menggunakan Custom Component

### Setup
```php
use Modules\RajaCms\Filament\Forms\Components\RajaCmsFileUpload;
```

### Penggunaan
```php
RajaCmsFileUpload::make('gambar')
    ->label('Gambar Utama')
    ->cmsDirectory('cms')
    ->columnSpanFull(),

RajaCmsFileUpload::make('article_image')
    ->label('Gambar Artikel')
    ->articlesDirectory()
    ->columnSpanFull(),

RajaCmsFileUpload::make('page_banner')
    ->label('Banner Halaman')
    ->pagesDirectory()
    ->columnSpanFull(),

RajaCmsFileUpload::make('banner')
    ->label('Banner')
    ->bannersDirectory()
    ->columnSpanFull(),
```

## 3. Menggunakan Service Langsung

### Setup
```php
use Modules\RajaCms\Services\RajaCmsConfigService;
```

### Penggunaan
```php
FileUpload::make('gambar')
    ->label('Gambar Utama')
    ->disk(app(RajaCmsConfigService::class)->getStorageDisk())
    ->directory(app(RajaCmsConfigService::class)->getFullUploadPath('cms'))
    ->maxSize(app(RajaCmsConfigService::class)->getMaxFileSize())
    ->columnSpanFull(),
```

## Konfigurasi

### File: `Modules/RajaCms/config/config.php`
```php
return [
    'storage' => [
        'disk' => env('RAJACMS_STORAGE_DISK', 'public'),
        'base_path' => env('RAJACMS_BASE_PATH', 'uploads'),
        'url_prefix' => env('RAJACMS_URL_PREFIX', '/storage'),
    ],

    'upload' => [
        'max_file_size' => env('RAJACMS_MAX_FILE_SIZE', 10240), // KB
        'max_files' => env('RAJACMS_MAX_FILES', 10),
        'temporary_directory' => env('RAJACMS_TEMP_DIR', 'livewire-tmp'),
    ],

    'directories' => [
        'cms' => 'cms',
        'articles' => 'articles',
        'pages' => 'pages',
        'banners' => 'banners',
    ],
];
```

### Environment Variables (.env)
```env
RAJACMS_STORAGE_DISK=public
RAJACMS_BASE_PATH=uploads
RAJACMS_URL_PREFIX=/storage
RAJACMS_MAX_FILE_SIZE=10240
RAJACMS_MAX_FILES=10
RAJACMS_TEMP_DIR=livewire-tmp
```

## Path Structure

Dengan konfigurasi default, file akan disimpan di:
- **CMS**: `/storage/uploads/cms/`
- **Articles**: `/storage/uploads/articles/`
- **Pages**: `/storage/uploads/pages/`
- **Banners**: `/storage/uploads/banners/`

## Contoh Lengkap

```php
<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Forms;

use Filament\Forms\Components\Grid;
use Modules\RajaCms\Traits\PakaiRajaGambar;

class InfoKonten
{
    use PakaiRajaGambar;

    public static function make(): Grid
    {
        return Grid::make('Info Konten')->schema([
            // Gambar utama menggunakan trait
            self::makeCmsFileUpload('gambar', 'cms')
                ->label('Gambar Utama')
                ->columnSpanFull(),

            // Gambar artikel
            self::makeArticleFileUpload('featured_image')
                ->label('Gambar Unggulan')
                ->columnSpanFull(),

            // Banner halaman
            self::makePageFileUpload('banner')
                ->label('Banner Halaman')
                ->columnSpanFull(),
        ]);
    }
}
```

## Keuntungan

1. **Konsistensi**: Semua upload mengikuti struktur path yang sama
2. **Konfigurasi Terpusat**: Path dapat diubah dari config tanpa mengubah kode
3. **Environment Specific**: Dapat berbeda per environment (dev, staging, production)
4. **Type Safety**: Method trait memberikan type safety dan autocomplete
5. **Maintainability**: Mudah maintenance dan update

## Tips

- Gunakan **Trait PakaiRajaGambar** untuk kemudahan dan konsistensi
- Gunakan **Custom Component** jika butuh fitur tambahan
- Gunakan **Service langsung** untuk kasus khusus atau kontrol penuh
