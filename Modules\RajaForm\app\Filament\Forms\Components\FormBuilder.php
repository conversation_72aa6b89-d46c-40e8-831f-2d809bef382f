<?php
// FormBuilder - Komponen untuk menggunakan form yang dibuat dengan FormGen
// Kompatibel dengan FieldBuilder6 dan sistem form generator
namespace Modules\RajaForm\Filament\Forms\Components;

use Modules\RajaForm\Models\FormGen;
use Modules\RajaForm\Filament\Forms\Components\FieldBuilder6;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Illuminate\Support\Str;

/**
 * FormBuilder - Komponen untuk menggunakan form yang dibuat dengan FormGen
 * Kompatibel dengan FieldBuilder6 dan sistem form generator
 */
class FormBuilder
{
    /**
     * Membuat fields dari form yang tersimpan di database
     *
     * @param string|int $formIdentifier Kunci form (string) atau ID form (integer) di database
     * @param bool $gunakanHidden Aktifkan hidden field
     * @param string $pesanKosong Pesan jika form tidak ditemukan
     * @return array
     */
    public static function make(
        string|int $formIdentifier,
        bool $gunakanHidden = true,
        string $pesanKosong = 'Form tidak ditemukan'
    ): array {
        // Cari form di database berdasarkan ID atau key
        $formData = static::getFormData($formIdentifier);

        if (!$formData) {
            return [
                Placeholder::make('form_not_found')
                    ->content("Form '{$formIdentifier}' {$pesanKosong}")
                    ->extraAttributes(['class' => 'text-red-500 italic'])
            ];
        }

        // Gunakan FieldBuilder6 untuk kompatibilitas
        return FieldBuilder6::buatFields(
            $formData,
            'info_form',
            $gunakanHidden,
            $pesanKosong
        );
    }

    /**
     * Membuat fields dengan wrapper Section
     *
     * @param string|int $formIdentifier Kunci form (string) atau ID form (integer) di database
     * @param string $sectionTitle Judul section
     * @param bool $gunakanHidden Aktifkan hidden field
     * @return Section
     */
    public static function makeWithSection(
        string|int $formIdentifier,
        string $sectionTitle,
        bool $gunakanHidden = true
    ): Section {
        return Section::make($sectionTitle)
            ->schema(static::make($formIdentifier, $gunakanHidden));
    }

    /**
     * Membuat fields dengan wrapper Grid
     *
     * @param string|int $formIdentifier Kunci form (string) atau ID form (integer) di database
     * @param int $columns Jumlah kolom grid
     * @param bool $gunakanHidden Aktifkan hidden field
     * @return Grid
     */
    public static function makeWithGrid(
        string|int $formIdentifier,
        int $columns = 2,
        bool $gunakanHidden = true
    ): Grid {
        return Grid::make($columns)
            ->schema(static::make($formIdentifier, $gunakanHidden));
    }

    /**
     * Mengecek apakah form ada di database
     *
     * @param string|int $formIdentifier Kunci form (string) atau ID form (integer)
     * @return bool
     */
    public static function exists(string|int $formIdentifier): bool
    {
        if (is_numeric($formIdentifier)) {
            return FormGen::where('id', $formIdentifier)->exists();
        }

        return FormGen::where('key', $formIdentifier)->exists();
    }

    /**
     * Mendapatkan daftar form yang tersedia
     *
     * @return array
     */
    public static function getAvailableForms(): array
    {
        return FormGen::pluck('key', 'key')->toArray();
    }

    /**
     * Mendapatkan daftar form dengan ID dan key untuk debugging
     *
     * @return array
     */
    public static function getAvailableFormsWithId(): array
    {
        return FormGen::select('id', 'key', 'nama')
            ->get()
            ->mapWithKeys(function ($form) {
                $label = $form->nama ? "{$form->nama} (ID: {$form->id}, Key: {$form->key})" : "ID: {$form->id}, Key: {$form->key}";
                return [$form->id => $label];
            })
            ->toArray();
    }

    /**
     * Mendapatkan preview JSON untuk debugging
     *
     * @param string|int $formIdentifier Kunci form (string) atau ID form (integer)
     * @return array|null
     */
    public static function getPreview(string|int $formIdentifier): ?array
    {
        $formData = static::getFormData($formIdentifier);

        if (!$formData) {
            return null;
        }

        // Parse JSON jika berupa string
        if (is_string($formData)) {
            return json_decode($formData, true);
        }

        return $formData;
    }

    /**
     * Mengambil data form dari database
     *
     * @param string|int $formIdentifier Kunci form (string) atau ID form (integer)
     * @return array|string|null
     */
    protected static function getFormData(string|int $formIdentifier): array|string|null
    {
        // Tentukan apakah pencarian berdasarkan ID atau key
        if (is_numeric($formIdentifier)) {
            $form = FormGen::where('id', $formIdentifier)->first();
        } else {
            $form = FormGen::where('key', $formIdentifier)->first();
        }

        if (!$form) {
            return null;
        }

        // Prioritaskan kolom 'value' sesuai preferensi user
        // Jika value kosong, fallback ke json
        if (!empty($form->value)) {
            return static::normalizeFormData($form->value);
        }

        if (!empty($form->json)) {
            return static::normalizeFormData($form->json);
        }

        return null;
    }

    /**
     * Normalisasi data form agar kompatibel dengan FieldBuilder6
     *
     * @param mixed $data Data form
     * @return array|string
     */
    protected static function normalizeFormData($data): array|string
    {
        // Jika sudah array, pastikan format sesuai FieldBuilder6
        if (is_array($data)) {
            return static::convertToFieldBuilder6Format($data);
        }

        // Jika string JSON, parse dan normalisasi
        if (is_string($data)) {
            $parsed = json_decode($data, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($parsed)) {
                return static::convertToFieldBuilder6Format($parsed);
            }
            return $data; // Return as-is jika tidak bisa di-parse
        }

        return [];
    }

    /**
     * Konversi format data ke format yang kompatibel dengan FieldBuilder6
     *
     * @param array $data Data array
     * @return array
     */
    protected static function convertToFieldBuilder6Format(array $data): array
    {
        $converted = [];

        foreach ($data as $item) {
            if (!is_array($item)) {
                continue;
            }

            $field = [];

            // Mapping field yang diperlukan FieldBuilder6
            $field['type'] = $item['type'] ?? 'textinput';
            $field['name'] = $item['name'] ?? '';

            // Auto-generate label jika tidak ada
            if (isset($item['label']) && !empty($item['label'])) {
                $field['label'] = $item['label'];
            } elseif (isset($item['field_label']) && $item['field_label'] === 'custom' && !empty($item['label'])) {
                $field['label'] = $item['label'];
            } elseif (!isset($item['field_label']) || $item['field_label'] === 'auto') {
                // Auto-generate label dari nama field
                $field['label'] = static::generateAutoLabel($field['name']);
            }
            // Jika field_label === 'false', tidak ada label

            // Mapping properti lainnya
            if (isset($item['required'])) {
                $field['required'] = $item['required'];
            }

            if (isset($item['placeholder'])) {
                $field['placeholder'] = $item['placeholder'];
            }

            if (isset($item['tooltip'])) {
                $field['tooltip'] = $item['tooltip'];
            }

            if (isset($item['helperText'])) {
                $field['helperText'] = $item['helperText'];
            }

            if (isset($item['options'])) {
                $field['options'] = $item['options'];
            }

            if (isset($item['columnspan'])) {
                $field['columnspan'] = $item['columnspan'];
            }

            if (isset($item['disabled'])) {
                $field['disabled'] = $item['disabled'];
            }

            if (isset($item['readonly'])) {
                $field['readonly'] = $item['readonly'];
            }

            if (isset($item['default'])) {
                $field['default'] = $item['default'];
            }

            // Tambahkan properti khusus untuk jenis field tertentu
            if ($field['type'] === 'textarea' && isset($item['tinggi'])) {
                $field['tinggi'] = $item['tinggi'];
            }

            if ($field['type'] === 'textinput' && isset($item['mask'])) {
                $field['mask'] = $item['mask'];
            }

            // Properti khusus untuk KeyValue
            if ($field['type'] === 'keyvalue') {
                $keyValueProps = [
                    'key_label', 'value_label', 'key_placeholder', 'value_placeholder',
                    'add_action_label', 'addable', 'deletable', 'editable_keys',
                    'editable_values', 'reorderable'
                ];

                foreach ($keyValueProps as $prop) {
                    if (isset($item[$prop])) {
                        $field[$prop] = $item[$prop];
                    }
                }
            }

            // Properti khusus untuk TagsInput
            if ($field['type'] === 'tagsinput') {
                $tagsProps = [
                    'separator', 'suggestions', 'split_keys', 'tag_prefix',
                    'tag_suffix', 'color', 'reorderable', 'nested_rules'
                ];

                foreach ($tagsProps as $prop) {
                    if (isset($item[$prop])) {
                        $field[$prop] = $item[$prop];
                    }
                }
            }

            // Properti khusus untuk RajaGambar
            if ($field['type'] === 'rajagambar') {
                $rajaGambarProps = [
                    'collection', 'multiple', 'max_files', 'directory_type',
                    'accepted_types'
                ];

                foreach ($rajaGambarProps as $prop) {
                    if (isset($item[$prop])) {
                        $field[$prop] = $item[$prop];
                    }
                }
            }

            $converted[] = $field;
        }

        return $converted;
    }

    /**
     * Generate label otomatis dari nama field
     * Mengkonversi snake_case/camelCase ke Title Case
     *
     * @param string $fieldName Nama field
     * @return string
     */
    protected static function generateAutoLabel(string $fieldName): string
    {
        // Konversi snake_case ke spasi
        $label = str_replace('_', ' ', $fieldName);
        $label = str_replace('.', ' ', $fieldName);

        // Konversi camelCase ke spasi
        $label = preg_replace('/([a-z])([A-Z])/', '$1 $2', $label);

        // Konversi ke Title Case
        return Str::title($label);
    }
}
