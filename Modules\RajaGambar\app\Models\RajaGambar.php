<?php

namespace Modules\RajaGambar\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use App\Models\User;

class RajaGambar extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $table = 'raja_gambar';

    protected $fillable = [
        'file',
        'json',
        'user_id',
        'toko_id',
        'model_type',
        'model_id',
        'field_name',
    ];

    protected $casts = [
        'json' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that uploaded this file
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full file path
     */
    public function getFullPathAttribute(): string
    {
        if (str_starts_with($this->file, '/')) {
            return $this->file;
        }

        if (str_starts_with($this->file, 'storage/')) {
            return public_path($this->file);
        }

        return storage_path('app/public/' . $this->file);
    }

    /**
     * Get the file URL
     */
    public function getUrlAttribute(): string
    {
        if (str_starts_with($this->file, 'http')) {
            return $this->file;
        }

        if (str_starts_with($this->file, '/storage/')) {
            return url($this->file);
        }

        return Storage::url($this->file);
    }

    /**
     * Get file size in human readable format
     */
    public function getFileSizeHumanAttribute(): string
    {
        $bytes = $this->json['file_size'] ?? 0;

        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Check if file exists
     */
    public function fileExists(): bool
    {
        return file_exists($this->full_path);
    }

    /**
     * Get image dimensions
     */
    public function getDimensionsAttribute(): ?string
    {
        $width = $this->json['width'] ?? null;
        $height = $this->json['height'] ?? null;

        if ($width && $height) {
            return "{$width} x {$height}";
        }

        return null;
    }

    /**
     * Scope for active files (not deleted)
     */
    public function scopeActive($query)
    {
        return $query->whereNull('json->deleted_at');
    }

    /**
     * Scope for deleted files
     */
    public function scopeDeleted($query)
    {
        return $query->whereNotNull('json->deleted_at');
    }

    /**
     * Scope by model type
     */
    public function scopeByModel($query, string $modelType, $modelId = null)
    {
        $query->where('json->model_type', $modelType);

        if ($modelId !== null) {
            $query->where('json->model_id', $modelId);
        }

        return $query;
    }

    /**
     * Scope by field name
     */
    public function scopeByField($query, string $fieldName)
    {
        return $query->where('json->field_name', $fieldName);
    }

    /**
     * Scope by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('json->user_id', $userId);
    }

   //  public function registerMediaConversions(?Media $media = null): void
   //  {
        // Nonaktifkan conversions yang menggunakan direktori conversions
        // Thumbnail akan dibuat menggunakan RajaGambarThumbnailService custom
   //  }
}

