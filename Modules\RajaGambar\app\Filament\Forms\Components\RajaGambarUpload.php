<?php

namespace Modules\RajaGambar\Filament\Forms\Components;

use Filament\Forms\Components\FileUpload;
use Modules\RajaGambar\Services\RajaGambarConfigService;

class RajaGambarUpload extends FileUpload
{
    protected string $view = 'filament-forms::components.file-upload';
    
    protected RajaGambarConfigService $configService;
    protected string $baseDirectory = 'images';
    protected ?string $customSubdirectory = null;

    public static function make(string $name): static
    {
        $static = parent::make($name);
        $static->configService = app(RajaGambarConfigService::class);
        $static->setupDefaults();
        return $static;
    }

    protected function setupDefaults(): void
    {
        $this->disk($this->configService->getStorageDisk());
        $this->updateDirectory();
        
        // Set default configurations
        $this->acceptedFileTypes($this->configService->getAcceptedFileTypes())
            ->maxSize($this->configService->getMaxFileSize())
            ->maxFiles($this->configService->getMaxFiles());
    }

    /**
     * Set base directory from config
     */
    public function imagesDirectory(): static
    {
        $this->baseDirectory = 'images';
        $this->updateDirectory();
        return $this;
    }

    public function galleryDirectory(): static
    {
        $this->baseDirectory = 'gallery';
        $this->updateDirectory();
        return $this;
    }

    public function productsDirectory(): static
    {
        $this->baseDirectory = 'products';
        $this->updateDirectory();
        return $this;
    }

    public function bannersDirectory(): static
    {
        $this->baseDirectory = 'banners';
        $this->updateDirectory();
        return $this;
    }

    public function avatarsDirectory(): static
    {
        $this->baseDirectory = 'avatars';
        $this->updateDirectory();
        return $this;
    }

    public function thumbnailsDirectory(): static
    {
        $this->baseDirectory = 'thumbnails';
        $this->updateDirectory();
        return $this;
    }

    public function tempDirectory(): static
    {
        $this->baseDirectory = 'temp';
        $this->updateDirectory();
        return $this;
    }

    /**
     * Set custom subdirectory
     */
    public function subdirectory(string $subdirectory): static
    {
        $this->customSubdirectory = $subdirectory;
        $this->updateDirectory();
        return $this;
    }

    /**
     * Alias for subdirectory method
     */
    public function collection(string $collection): static
    {
        return $this->subdirectory($collection);
    }

    /**
     * Update the directory path based on base directory and subdirectory
     */
    protected function updateDirectory(): void
    {
        $fullPath = $this->customSubdirectory 
            ? $this->configService->getFullUploadPath($this->baseDirectory) . '/' . $this->customSubdirectory
            : $this->configService->getFullUploadPath($this->baseDirectory);
            
        parent::directory($fullPath);
    }

    /**
     * Configure for single image upload
     */
    public function singleImage(): static
    {
        return $this->maxFiles(1)
            ->image()
            ->imageEditor()
            ->imageEditorAspectRatios([
                null,
                '16:9',
                '4:3',
                '1:1',
            ]);
    }

    /**
     * Configure for multiple image upload
     */
    public function multipleImages(?int $maxFiles = null): static
    {
        $maxFiles = $maxFiles ?? $this->configService->getMaxFiles();
        
        return $this->maxFiles($maxFiles)
            ->image()
            ->multiple()
            ->reorderable()
            ->imageEditor()
            ->imageEditorAspectRatios([
                null,
                '16:9',
                '4:3',
                '1:1',
            ]);
    }

    /**
     * Configure for gallery upload
     */
    public function gallery(?int $maxFiles = null): static
    {
        return $this->galleryDirectory()
            ->multipleImages($maxFiles)
            ->imagePreview()
            ->panelLayout('grid');
    }

    /**
     * Configure for avatar upload
     */
    public function avatar(): static
    {
        return $this->avatarsDirectory()
            ->singleImage()
            ->avatar()
            ->imageEditor()
            ->imageEditorAspectRatios(['1:1'])
            ->circleCropper();
    }

    /**
     * Configure for banner upload
     */
    public function banner(): static
    {
        return $this->bannersDirectory()
            ->singleImage()
            ->imageEditor()
            ->imageEditorAspectRatios(['16:9', '21:9', '3:1']);
    }

    /**
     * Configure for product image upload
     */
    public function productImage(int $maxFiles = 5): static
    {
        return $this->productsDirectory()
            ->multipleImages($maxFiles)
            ->imagePreview()
            ->panelLayout('grid');
    }

    /**
     * Get the full storage path
     */
    public function getStoragePath(): string
    {
        return $this->customSubdirectory 
            ? $this->configService->getFullUploadPath($this->baseDirectory) . '/' . $this->customSubdirectory
            : $this->configService->getFullUploadPath($this->baseDirectory);
    }

    /**
     * Get the base directory
     */
    public function getBaseDirectory(): string
    {
        return $this->baseDirectory;
    }

    /**
     * Get the custom subdirectory
     */
    public function getCustomSubdirectory(): ?string
    {
        return $this->customSubdirectory;
    }
}
