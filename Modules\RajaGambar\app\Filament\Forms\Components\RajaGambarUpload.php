<?php

namespace Modules\RajaGambar\Filament\Forms\Components;

use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Log;
use Modules\RajaGambar\Services\RajaGambarConfigService;
use Modules\RajaGambar\Traits\GeneratesThumbnails;

class RajaGambarUpload extends FileUpload
{
    use GeneratesThumbnails;

    protected string $view = 'filament-forms::components.file-upload';

    protected RajaGambarConfigService $configService;
    protected string $baseDirectory = 'images';
    protected ?string $customSubdirectory = null;

    public static function make(string $name): static
    {
        $static = parent::make($name);
        $static->configService = app(RajaGambarConfigService::class);
        $static->setupDefaults();
        return $static;
    }

    protected function setupDefaults(): void
    {
        $this->disk($this->configService->getStorageDisk());
        $this->updateDirectory();

        // Set default configurations
        $this->acceptedFileTypes($this->configService->getAcceptedFileTypes())
            ->maxSize($this->configService->getMaxFileSize())
            ->maxFiles($this->configService->getMaxFiles());

        // Setup thumbnail generation callbacks
        $this->setupThumbnailCallbacks();
    }

    /**
     * Setup thumbnail generation callbacks
     */
    protected function setupThumbnailCallbacks(): void
    {
        // Generate thumbnails after files are uploaded and saved
        $this->afterStateUpdated(function ($state, $component) {
            if (!empty($state)) {
                $component->generateThumbnailsAfterUpload($state);
            }
        });

        // Fix database storage format - convert array to string for single image
        $this->dehydrateStateUsing(function ($state) {
            if (is_array($state) && !empty($state) && count($state) > 0) {
                // For single image, return just the string, not array
                if (count($state) === 1 && isset($state[0])) {
                    return $state[0];
                }
                // For multiple images, return as array (will be JSON encoded by Laravel)
                return $state;
            }
            return $state;
        });

        // Handle loading data from database
        $this->afterStateHydrated(function ($component, $state) {
            if (is_string($state) && !empty($state)) {
                // If it's a JSON string, decode it
                if (str_starts_with($state, '[') && str_ends_with($state, ']')) {
                    $decoded = json_decode($state, true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                        $component->state($decoded);
                        return;
                    }
                }
                // If it's a single file path, convert to array for FilamentPHP
                $component->state([$state]);
            }
        });
    }

    /**
     * Generate thumbnails after file upload
     */
    public function generateThumbnailsAfterUpload($filePaths): void
    {
        // Create log file for debugging
        $logFile = storage_path('logs/rajagambar_debug.log');
        $logMessage = date('Y-m-d H:i:s') . " - generateThumbnailsAfterUpload called\n";
        $logMessage .= "FilePaths: " . json_encode($filePaths) . "\n";
        $logMessage .= "AutoGenerateThumbnails: " . ($this->autoGenerateThumbnails ? 'true' : 'false') . "\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

        Log::info('generateThumbnailsAfterUpload called', [
            'filePaths' => $filePaths,
            'autoGenerateThumbnails' => $this->autoGenerateThumbnails
        ]);

        if (!$this->autoGenerateThumbnails) {
            Log::info('Thumbnail generation disabled');
            file_put_contents($logFile, "Thumbnail generation disabled\n", FILE_APPEND | LOCK_EX);
            return;
        }

        // Handle TemporaryUploadedFile objects from Livewire
        $processedPaths = [];

        if (is_array($filePaths)) {
            foreach ($filePaths as $key => $file) {
                if (is_object($file) && method_exists($file, 'getRealPath')) {
                    // This is a TemporaryUploadedFile
                    $realPath = $file->getRealPath();
                    if ($realPath && file_exists($realPath)) {
                        $processedPaths[] = $realPath;
                        Log::info('Processed TemporaryUploadedFile', [
                            'key' => $key,
                            'realPath' => $realPath
                        ]);
                    }
                } elseif (is_string($file)) {
                    $processedPaths[] = $file;
                } elseif (is_string($key)) {
                    // Sometimes the file path is the key
                    $processedPaths[] = $key;
                }
            }
        } else {
            $processedPaths = [$filePaths];
        }

        if (empty($processedPaths)) {
            Log::info('No valid file paths found for thumbnail generation');
            return;
        }

        // Get base directory for thumbnails
        $baseDirectory = $this->customSubdirectory ?? $this->baseDirectory;

        Log::info('Generating thumbnails', [
            'processedPaths' => $processedPaths,
            'baseDirectory' => $baseDirectory
        ]);

        // Generate thumbnails
        $this->generateThumbnailsForFiles($processedPaths, $baseDirectory);
    }

    /**
     * Set base directory from config
     */
    public function imagesDirectory(): static
    {
        $this->baseDirectory = 'images';
        $this->updateDirectory();
        return $this;
    }

    public function galleryDirectory(): static
    {
        $this->baseDirectory = 'gallery';
        $this->updateDirectory();
        return $this;
    }

    public function productsDirectory(): static
    {
        $this->baseDirectory = 'products';
        $this->updateDirectory();
        return $this;
    }

    public function bannersDirectory(): static
    {
        $this->baseDirectory = 'banners';
        $this->updateDirectory();
        return $this;
    }

    public function avatarsDirectory(): static
    {
        $this->baseDirectory = 'avatars';
        $this->updateDirectory();
        return $this;
    }

    public function thumbnailsDirectory(): static
    {
        $this->baseDirectory = 'thumbnails';
        $this->updateDirectory();
        return $this;
    }

    public function tempDirectory(): static
    {
        $this->baseDirectory = 'temp';
        $this->updateDirectory();
        return $this;
    }

    /**
     * Set custom collection/subdirectory for file storage
     */
    public function koleksi(string $koleksi): static
    {
        $this->customSubdirectory = $koleksi;
        $this->updateDirectory();
        return $this;
    }

    /**
     * Alias for koleksi method (English version)
     */
    public function collection(string $collection): static
    {
        return $this->koleksi($collection);
    }

    /**
     * Alias for koleksi method (backward compatibility)
     */
    public function subdirectory(string $subdirectory): static
    {
        return $this->koleksi($subdirectory);
    }

    /**
     * Update the directory path based on base directory and subdirectory
     */
    protected function updateDirectory(): void
    {
        // Jika ada koleksi/subdirectory, gunakan langsung tanpa base directory
        // Path akan menjadi /storage/uploads/{koleksi}/ bukan /storage/uploads/images/{koleksi}/
        $fullPath = $this->customSubdirectory
            ? $this->configService->getBaseUploadPath() . '/' . $this->customSubdirectory
            : $this->configService->getFullUploadPath($this->baseDirectory);

        parent::directory($fullPath);
    }

    /**
     * Configure for single image upload
     */
    public function singleImage(): static
    {
        return $this->maxFiles(1)
            ->image()
            ->imageEditor()
            ->imageEditorAspectRatios([
                null,
                '16:9',
                '4:3',
                '1:1',
            ]);
    }

    /**
     * Configure for multiple image upload
     */
    public function multipleImages(?int $maxFiles = null): static
    {
        $maxFiles = $maxFiles ?? $this->configService->getMaxFiles();
        
        return $this->maxFiles($maxFiles)
            ->image()
            ->multiple()
            ->reorderable()
            ->imageEditor()
            ->imageEditorAspectRatios([
                null,
                '16:9',
                '4:3',
                '1:1',
            ]);
    }

    /**
     * Configure for gallery upload
     */
    public function gallery(?int $maxFiles = null): static
    {
        return $this->galleryDirectory()
            ->multipleImages($maxFiles)
            ->imagePreviewHeight('200px')
            ->panelLayout('grid')
            ->galleryThumbnails();
    }

    /**
     * Configure for avatar upload
     */
    public function avatar(): static
    {
        return $this->avatarsDirectory()
            ->singleImage()
            ->avatar()
            ->imageEditor()
            ->imageEditorAspectRatios(['1:1'])
            ->circleCropper()
            ->avatarThumbnails();
    }

    /**
     * Configure for banner upload
     */
    public function banner(): static
    {
        return $this->bannersDirectory()
            ->singleImage()
            ->imageEditor()
            ->imageEditorAspectRatios(['16:9', '21:9', '3:1']);
    }

    /**
     * Configure for product image upload
     */
    public function productImage(int $maxFiles = 5): static
    {
        return $this->productsDirectory()
            ->multipleImages($maxFiles)
            ->imagePreviewHeight('200px')
            ->panelLayout('grid')
            ->productThumbnails();
    }

    /**
     * Get the full storage path
     */
    public function getStoragePath(): string
    {
        return $this->customSubdirectory
            ? $this->configService->getBaseUploadPath() . '/' . $this->customSubdirectory
            : $this->configService->getFullUploadPath($this->baseDirectory);
    }

    /**
     * Get the base directory
     */
    public function getBaseDirectory(): string
    {
        return $this->baseDirectory;
    }

    /**
     * Get the custom subdirectory
     */
    public function getCustomSubdirectory(): ?string
    {
        return $this->customSubdirectory;
    }

    /**
     * Enable thumbnails with config-based sizes
     */
    public function withThumbnails(bool $enabled = true): static
    {
        return $this->autoGenerateThumbnails($enabled);
    }

    /**
     * Enable WebP conversion for thumbnails and originals
     */
    public function withWebpConversion(bool $enabled = true, bool $deleteOriginal = false): static
    {
        return $this->convertToWebp($enabled)
            ->deleteOriginalAfterWebp($deleteOriginal);
    }

    /**
     * Configure thumbnails based on config file settings
     */
    public function configBasedThumbnails(): static
    {
        $this->initializeThumbnailService();

        // Get thumbnail configuration from config file
        $thumbnailConfig = $this->getThumbnailConfig();

        if (!empty($thumbnailConfig)) {
            $this->thumbnailSizes($thumbnailConfig);
        }

        return $this->autoGenerateThumbnails(true)
            ->convertToWebp(true);
    }

    /**
     * Configure for CMS images with percentage-based thumbnails
     */
    public function cmsImages(): static
    {
        return $this->imagesDirectory()
            ->multipleImages()
            ->imagePreviewHeight('200px')
            ->panelLayout('grid')
            ->thumbnailSizes([
                '75p' => ['percentage' => 75, 'quality' => 80, 'enabled' => true],
                '50p' => ['percentage' => 50, 'quality' => 80, 'enabled' => true],
                '25p' => ['percentage' => 25, 'quality' => 75, 'enabled' => true],
            ])
            ->autoGenerateThumbnails(true)
            ->convertToWebp(true);
    }

    /**
     * Configure for banner images with specific sizes
     */
    public function bannerImages(): static
    {
        return $this->bannersDirectory()
            ->singleImage()
            ->imageEditor()
            ->imageEditorAspectRatios(['16:9', '21:9', '3:1'])
            ->thumbnailSizes([
                'small' => ['width' => 300, 'height' => 169, 'quality' => 80, 'fit' => 'cover', 'enabled' => true],
                'medium' => ['width' => 600, 'height' => 338, 'quality' => 85, 'fit' => 'cover', 'enabled' => true],
                'large' => ['width' => 1200, 'height' => 675, 'quality' => 90, 'fit' => 'cover', 'enabled' => true],
            ])
            ->autoGenerateThumbnails(true)
            ->convertToWebp(true);
    }

    /**
     * Get thumbnail URL for uploaded file
     */
    public function getThumbnailUrlForFile(string $filename, string $sizeName): ?string
    {
        $baseDirectory = $this->customSubdirectory ?? $this->baseDirectory;
        $filePath = $this->getStoragePath() . '/' . $filename;

        return $this->getThumbnailUrl($filePath, $sizeName, $baseDirectory);
    }

    /**
     * Check if thumbnail exists for uploaded file
     */
    public function thumbnailExistsForFile(string $filename, string $sizeName): bool
    {
        $baseDirectory = $this->customSubdirectory ?? $this->baseDirectory;
        $filePath = $this->getStoragePath() . '/' . $filename;

        return $this->thumbnailExists($filePath, $sizeName, $baseDirectory);
    }
}
