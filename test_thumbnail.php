<?php

require_once 'vendor/autoload.php';

use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

try {
    echo "Testing thumbnail generation...\n";
    
    $manager = new ImageManager(new Driver());
    $image = $manager->read('storage/uploads/cms/01JZD2QPN5Q1MRKKFFNTQSMVN3.jpg');
    echo "Image loaded successfully: " . $image->width() . "x" . $image->height() . "\n";
    
    $outputPath = 'storage/uploads/cms/thumbnails/test_manual.webp';
    
    // Ensure directory exists
    $dir = dirname($outputPath);
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "Created directory: $dir\n";
    }
    
    echo "Directory writable: " . (is_writable($dir) ? 'YES' : 'NO') . "\n";
    
    $image->toWebp(80)->save($outputPath);
    
    if (file_exists($outputPath)) {
        echo "Manual WebP save successful: " . filesize($outputPath) . " bytes\n";
        echo "File path: $outputPath\n";
        // Don't cleanup for verification
    } else {
        echo "Manual WebP save failed\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
