<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Modules\RajaGambar\Services\ThumbnailService;

try {
    $service = app(ThumbnailService::class);
    echo "ThumbnailService loaded successfully\n";
    
    $result = $service->generateThumbnails('E:/www/hotel/public/noimage.jpg', 'test');
    echo "Result: ";
    var_dump($result);
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
