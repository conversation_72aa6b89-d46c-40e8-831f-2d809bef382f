<?php

namespace Modules\RajaGambar\Services;

use Illuminate\Support\Facades\Config;

class RajaGambarConfigService
{
    protected array $config;

    public function __construct()
    {
        $this->config = Config::get('rajagambar', []);
    }

    /**
     * Get storage disk name
     */
    public function getStorageDisk(): string
    {
        return $this->config['storage']['disk'] ?? 'public';
    }

    /**
     * Get base storage path
     */
    public function getBasePath(): string
    {
        return $this->config['storage']['path'] ?? 'uploads';
    }

    /**
     * Get URL prefix
     */
    public function getUrlPrefix(): string
    {
        return $this->config['storage']['url_prefix'] ?? '/storage';
    }

    /**
     * Get max file size in KB
     */
    public function getMaxFileSize(): int
    {
        return $this->config['upload']['max_file_size'] ?? 10240;
    }

    /**
     * Get max files count
     */
    public function getMaxFiles(): int
    {
        return $this->config['upload']['max_files'] ?? 10;
    }

    /**
     * Get temporary directory
     */
    public function getTempDirectory(): string
    {
        return $this->config['upload']['temporary_directory'] ?? 'livewire-tmp';
    }

    /**
     * Get directory path for specific type
     */
    public function getDirectory(string $type): string
    {
        return $this->config['directories'][$type] ?? $type;
    }

    /**
     * Get full upload path for directory
     */
    public function getFullUploadPath(string $directory): string
    {
        return $this->getBasePath() . '/' . $this->getDirectory($directory);
    }

    /**
     * Get full URL for uploaded file
     */
    public function getFileUrl(string $filePath): string
    {
        return $this->getUrlPrefix() . '/' . $filePath;
    }

    /**
     * Get all directories configuration
     */
    public function getDirectories(): array
    {
        return $this->config['directories'] ?? [];
    }

    /**
     * Get accepted file types
     */
    public function getAcceptedFileTypes(): array
    {
        return $this->config['security']['allowed_mime_types'] ?? [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];
    }

    /**
     * Get image quality setting
     */
    public function getImageQuality(): int
    {
        return $this->config['image']['quality'] ?? 85;
    }

    /**
     * Check if WebP conversion is enabled
     */
    public function isWebpEnabled(): bool
    {
        return $this->config['image']['webp']['enabled'] ?? true;
    }

    /**
     * Get WebP quality
     */
    public function getWebpQuality(): int
    {
        return $this->config['image']['webp']['quality'] ?? 80;
    }

    /**
     * Check if thumbnails are enabled
     */
    public function isThumbnailsEnabled(): bool
    {
        return $this->config['thumbnails']['enabled'] ?? true;
    }

    /**
     * Get thumbnail sizes configuration
     */
    public function getThumbnailSizes(): array
    {
        return $this->config['thumbnails']['sizes'] ?? [];
    }

    /**
     * Get thumbnail directory
     */
    public function getThumbnailDirectory(): string
    {
        return $this->config['thumbnails']['directory'] ?? 'thumbnails';
    }

    /**
     * Get image driver
     */
    public function getImageDriver(): string
    {
        return $this->config['image']['driver'] ?? 'gd';
    }

    /**
     * Get max image dimensions
     */
    public function getMaxImageDimensions(): array
    {
        return [
            'width' => $this->config['image']['max_width'] ?? 2048,
            'height' => $this->config['image']['max_height'] ?? 2048,
        ];
    }

    /**
     * Check if image optimization is enabled
     */
    public function isOptimizationEnabled(): bool
    {
        return $this->config['processing']['optimize'] ?? true;
    }

    /**
     * Get watermark configuration
     */
    public function getWatermarkConfig(): array
    {
        return $this->config['processing']['watermark'] ?? [];
    }

    /**
     * Check if watermark is enabled
     */
    public function isWatermarkEnabled(): bool
    {
        return $this->config['processing']['watermark']['enabled'] ?? false;
    }

    /**
     * Get naming pattern
     */
    public function getNamingPattern(): string
    {
        return $this->config['naming']['pattern'] ?? '{timestamp}_{random}_{original}';
    }

    /**
     * Get timestamp format for naming
     */
    public function getTimestampFormat(): string
    {
        return $this->config['naming']['timestamp_format'] ?? 'YmdHis';
    }

    /**
     * Get random length for naming
     */
    public function getRandomLength(): int
    {
        return $this->config['naming']['random_length'] ?? 8;
    }

    /**
     * Check if filename sanitization is enabled
     */
    public function isSanitizeFilenameEnabled(): bool
    {
        return $this->config['naming']['sanitize_filename'] ?? true;
    }

    /**
     * Get memory limit
     */
    public function getMemoryLimit(): string
    {
        return $this->config['performance']['memory_limit'] ?? '256M';
    }

    /**
     * Get processing timeout
     */
    public function getTimeout(): int
    {
        return $this->config['performance']['timeout'] ?? 30;
    }

    /**
     * Check if queue processing is enabled
     */
    public function isQueueProcessingEnabled(): bool
    {
        return $this->config['performance']['queue_processing'] ?? false;
    }

    /**
     * Check if logging is enabled
     */
    public function isLoggingEnabled(): bool
    {
        return $this->config['logging']['enabled'] ?? true;
    }

    /**
     * Get log level
     */
    public function getLogLevel(): string
    {
        return $this->config['logging']['level'] ?? 'info';
    }
}
