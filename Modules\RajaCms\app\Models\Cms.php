<?php

namespace Modules\RajaCms\Models;

use App\Models\KategoriArtikel;
use Mo<PERSON>les\RajaJson\Traits\HasRajajsonFields;
use App\Traits\PakaiJcol;
use <PERSON>tlechin\FilamentMenuBuilder\Concerns\HasMenuPanel;
use Datlechin\FilamentMenuBuilder\Contracts\MenuPanelable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\RajaGambar\Traits\PakaiRajaGambar;

use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
 

class Cms extends Model implements MenuPanelable 
{
    use HasMenuPanel, HasFactory, PakaiJcol, HasRajajsonFields, InteractsWithMedia, PakaiRajaGambar;

    protected $casts = [
        'json' => 'array',
        'location' => 'array',
    ];

    /**
     * Define which fields contain file uploads for RajaGambar trait
     */
    protected $fileUploadFields = [
        'gambar',
        'banner',
        'foto',
        'image',
        'thumbnail',
        'slideshow', // JCOL field that may contain images
        'pembicara',  // JCOL field that may contain images
        'acara',      // JCOL field that may contain images
    ];

    public function getTable()
    {
        return config('tabel.t_cms.nama_tabel');
    }

    
    public function getFillable()
    {
        $kolom = config('tabel.t_cms.kolom', []);
        if (!in_array('location', $kolom)) {
            $kolom[] = 'location';
        }
        return $kolom;
    }

    public function kategori()
    {
        return $this->belongsTo(KategoriArtikel::class, 'kategori_id');
    }

    public function kategoriArtikel()
    {
        return $this->belongsTo(KategoriArtikel::class, 'kategori_id');
    }

    public function getMenuPanelTitleColumn(): string
    {
        return 'Halaman';
    }

    public function getMenuPanelUrlUsing(): callable
    {
        return fn(self $model) => route('halaman', $model->slug);
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            if (!isset($model->attributes['isi']) && isset($model->getDirty()['isi'])) {
                $model->attributes['isi'] = $model->getDirty()['isi'];
            }
        });

        static::saved(function ($model) {
            if (($model->isi === null || strlen($model->isi) === 0) && isset($model->getDirty()['isi']) && strlen($model->getDirty()['isi']) > 0) {
                $model->isi = $model->getDirty()['isi'];
                $model->save();
            }
        });

        static::saving(function ($model) {
            if ($model->status === 'home' && $model->jenis !== 'HALAMAN') {
                $model->status = 'tampil';
            }
        });
    }

    public function setIsiAttribute($value)
    {
        if ($value !== null) {
            $this->attributes['isi'] = $value;
        }
    }

    public function getIsiAttribute($value)
    {
        return $value;
    }

    public function setJsonAttribute($value)
    {
        $this->attributes['json'] = is_array($value) ? json_encode($value) : $value;
    }

    // JCOL Accessors
    public function getPembicaraAttribute() { return $this->getJcol('pembicara', []); }
    public function getAcaraAttribute() { return $this->getJcol('acara', []); }
    public function getSlideshowAttribute() { return $this->getJcol('slideshow', []); }
    public function getSeoAttribute() { return $this->getJcol('seo', []); }

    public function setStatusAttribute($value)
    {
        if ($value === 'home' && $this->jenis !== 'HALAMAN') {
            $value = 'tampil';
        }
        $this->attributes['status'] = $value;
    }

    public static function getSlideShow($slug)
    {
        return self::where('slug', $slug)->value('slideshow');
    }

    public function getPetaAttribute() { return $this->getJcol('peta', []); }
    public function getGambar2Attribute() { return $this->getJcol('gambar2', ''); }
    public function getGambar2UrlAttribute() { $g2=$this->getGambar2Attribute(); return $g2?asset('storage/'.$g2):null; }
} 