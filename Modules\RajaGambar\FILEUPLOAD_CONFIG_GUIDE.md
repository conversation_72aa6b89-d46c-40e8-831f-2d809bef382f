# Panduan FileUpload dengan Config Path di RajaGambar

## Overview

Untuk membuat FileUpload mengikuti aturan path dari config.php (`/storage/uploads/{->directory()}`), tersedia 3 pendekatan di modul RajaGambar:

1. **Trait PakaiRajaGambar** (Recommended)
2. **Custom Component RajaGambarUploadExt**
3. **Service RajaGambarConfigService**

## 1. Menggunakan Trait PakaiRajaGambar (Recommended)

### Setup
```php
use Modules\RajaGambar\Traits\PakaiRajaGambar;

class InfoKonten
{
    use PakaiRajaGambar;
    
    public static function make(): Grid
    {
        return Grid::make('Info Konten')->schema([
            // FileUpload components here
        ]);
    }
}
```

### Penggunaan Basic
```php
// Basic image upload
self::makeImageUpload('gambar')
    ->label('Gambar Utama')
    ->columnSpanFull(),

// Gallery upload
self::makeGalleryUpload('gallery_images')
    ->label('Galeri Gambar')
    ->multiple()
    ->columnSpanFull(),

// Product upload
self::makeProductUpload('product_images')
    ->label('Gambar Produk')
    ->multiple()
    ->columnSpanFull(),

// Banner upload
self::makeBannerUpload('banner')
    ->label('Banner')
    ->columnSpanFull(),

// Avatar upload
self::makeAvatarUpload('avatar')
    ->label('Avatar')
    ->columnSpanFull(),
```

### Method yang Tersedia
- `makeRajaGambarFileUpload($name, $directory)` - Upload umum dengan custom directory
- `makeImageUpload($name)` - Upload untuk gambar umum
- `makeGalleryUpload($name)` - Upload untuk galeri
- `makeProductUpload($name)` - Upload untuk produk
- `makeBannerUpload($name)` - Upload untuk banner
- `makeAvatarUpload($name)` - Upload untuk avatar (dengan circle cropper)
- `makeThumbnailUpload($name)` - Upload untuk thumbnail
- `makeCustomDirectoryUpload($name, $directory)` - Upload dengan custom directory

## 2. Menggunakan Custom Component RajaGambarUploadExt

### Setup
```php
use Modules\RajaGambar\Filament\Forms\Components\RajaGambarUploadExt;
```

### Penggunaan
```php
// Basic usage
RajaGambarUploadExt::make('gambar')
    ->label('Gambar Utama')
    ->imagesDirectory()
    ->columnSpanFull(),

// Gallery with preset
RajaGambarUploadExt::make('gallery')
    ->label('Galeri')
    ->galleryUpload()
    ->columnSpanFull(),

// Product images with preset
RajaGambarUploadExt::make('product_images')
    ->label('Gambar Produk')
    ->productUpload()
    ->columnSpanFull(),

// Avatar with preset
RajaGambarUploadExt::make('avatar')
    ->label('Avatar')
    ->avatarUpload()
    ->columnSpanFull(),

// Banner with preset
RajaGambarUploadExt::make('banner')
    ->label('Banner')
    ->bannerUpload()
    ->columnSpanFull(),

// Custom directory with settings
RajaGambarUploadExt::make('custom_images')
    ->label('Custom Images')
    ->customDirectory('custom', [
        'max_files' => 5,
        'multiple' => true,
        'webp' => true,
        'thumbnails' => true,
        'optimize' => true,
        'image_editor' => true,
        'aspect_ratios' => ['16:9', '4:3']
    ])
    ->columnSpanFull(),
```

### Method Directory yang Tersedia
- `rajaDirectory($type)` - Set directory berdasarkan config
- `imagesDirectory()` - Directory untuk images
- `galleryDirectory()` - Directory untuk gallery
- `productsDirectory()` - Directory untuk products
- `bannersDirectory()` - Directory untuk banners
- `avatarsDirectory()` - Directory untuk avatars (dengan circle cropper)
- `thumbnailsDirectory()` - Directory untuk thumbnails

### Method Preset yang Tersedia
- `avatarUpload()` - Setup lengkap untuk avatar
- `galleryUpload()` - Setup lengkap untuk galeri
- `productUpload()` - Setup lengkap untuk produk
- `bannerUpload()` - Setup lengkap untuk banner
- `singleImage($directory)` - Setup untuk single image
- `multipleImages($directory, $maxFiles)` - Setup untuk multiple images
- `heroImage($directory)` - Setup untuk hero image

## 3. Menggunakan Service Langsung

### Setup
```php
use Modules\RajaGambar\Services\RajaGambarConfigService;
```

### Penggunaan
```php
FileUpload::make('gambar')
    ->label('Gambar Utama')
    ->disk(app(RajaGambarConfigService::class)->getStorageDisk())
    ->directory(app(RajaGambarConfigService::class)->getFullUploadPath('images'))
    ->maxSize(app(RajaGambarConfigService::class)->getMaxFileSize())
    ->acceptedFileTypes(app(RajaGambarConfigService::class)->getAcceptedFileTypes())
    ->columnSpanFull(),
```

## Konfigurasi

### File: `Modules/RajaGambar/config/config.php`
```php
return [
    'storage' => [
        'disk' => env('RAJAGAMBAR_STORAGE_DISK', 'public'),
        'path' => env('RAJAGAMBAR_STORAGE_PATH', 'uploads'),
        'url_prefix' => env('RAJAGAMBAR_URL_PREFIX', '/storage'),
    ],

    'directories' => [
        'images' => 'images',
        'gallery' => 'gallery',
        'products' => 'products',
        'banners' => 'banners',
        'avatars' => 'avatars',
        'thumbnails' => 'thumbnails',
        'temp' => 'temp',
    ],

    'upload' => [
        'max_file_size' => env('RAJAGAMBAR_MAX_FILE_SIZE', 10240), // KB
        'max_files' => env('RAJAGAMBAR_MAX_FILES', 10),
        'temporary_directory' => env('RAJAGAMBAR_TEMP_DIR', 'livewire-tmp'),
    ],

    'security' => [
        'allowed_mime_types' => [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp',
        ],
    ],
];
```

### Environment Variables (.env)
```env
RAJAGAMBAR_STORAGE_DISK=public
RAJAGAMBAR_STORAGE_PATH=uploads
RAJAGAMBAR_URL_PREFIX=/storage
RAJAGAMBAR_MAX_FILE_SIZE=10240
RAJAGAMBAR_MAX_FILES=10
RAJAGAMBAR_TEMP_DIR=livewire-tmp
```

## Path Structure

Dengan konfigurasi default, file akan disimpan di:
- **Images**: `/storage/uploads/images/`
- **Gallery**: `/storage/uploads/gallery/`
- **Products**: `/storage/uploads/products/`
- **Banners**: `/storage/uploads/banners/`
- **Avatars**: `/storage/uploads/avatars/`
- **Thumbnails**: `/storage/uploads/thumbnails/`

## Contoh Implementasi Lengkap

```php
<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Forms;

use Filament\Forms\Components\Grid;
use Modules\RajaGambar\Traits\PakaiRajaGambar;

class InfoKonten
{
    use PakaiRajaGambar;

    public static function make(): Grid
    {
        return Grid::make('Info Konten')->schema([
            // Gambar utama menggunakan trait
            self::makeImageUpload('gambar')
                ->label('Gambar Utama')
                ->columnSpanFull(),

            // Gambar kedua untuk JCOL
            self::makeImageUpload('jcol.gambar')
                ->label('Gambar Kedua')
                ->columnSpanFull(),

            // Banner
            self::makeBannerUpload('banner')
                ->label('Banner')
                ->columnSpanFull(),
        ]);
    }
}
```

## Keuntungan

1. **Konsistensi**: Semua upload mengikuti struktur path yang sama
2. **Konfigurasi Terpusat**: Path dapat diubah dari config tanpa mengubah kode
3. **Environment Specific**: Dapat berbeda per environment
4. **Type Safety**: Method trait memberikan type safety dan autocomplete
5. **Fitur Lengkap**: Terintegrasi dengan fitur RajaGambar (WebP, thumbnails, optimization)
6. **Database Recording**: Otomatis tercatat di tabel raja_gambar

## Tips

- Gunakan **Trait PakaiRajaGambar** untuk kemudahan dan konsistensi
- Gunakan **Custom Component** jika butuh fitur preset dan method chaining
- Gunakan **Service langsung** untuk kontrol penuh
- File akan otomatis tercatat di tabel `raja_gambar` jika model menggunakan trait `RecordsFileUploads`
