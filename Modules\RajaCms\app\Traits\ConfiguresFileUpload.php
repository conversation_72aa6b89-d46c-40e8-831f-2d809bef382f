<?php

namespace Modules\RajaCms\Traits;

use Filament\Forms\Components\FileUpload;
use Modules\RajaCms\Services\RajaCmsConfigService;

trait ConfiguresFileUpload
{
    /**
     * Configure FileUpload with RajaCms settings
     */
    public static function configureRajaCmsFileUpload(FileUpload $component, string $directory = 'cms'): FileUpload
    {
        $configService = app(RajaCmsConfigService::class);
        
        return $component
            ->disk($configService->getStorageDisk())
            ->directory($configService->getFullUploadPath($directory))
            ->maxSize($configService->getMaxFileSize())
            ->maxFiles($configService->getMaxFiles());
    }

    /**
     * Create configured FileUpload for CMS
     */
    public static function makeCmsFileUpload(string $name, string $directory = 'cms'): FileUpload
    {
        $component = FileUpload::make($name);
        return self::configureRajaCmsFileUpload($component, $directory);
    }

    /**
     * Create configured FileUpload for articles
     */
    public static function makeArticleFileUpload(string $name): FileUpload
    {
        return self::makeCmsFileUpload($name, 'articles');
    }

    /**
     * Create configured FileUpload for pages
     */
    public static function makePageFileUpload(string $name): FileUpload
    {
        return self::makeCmsFileUpload($name, 'pages');
    }

    /**
     * Create configured FileUpload for banners
     */
    public static function makeBannerFileUpload(string $name): FileUpload
    {
        return self::makeCmsFileUpload($name, 'banners');
    }
}
