<?php

namespace Modules\RajaGambar\Filament\Forms\Components;

use Filament\Forms\Components\FileUpload;
use Modules\RajaGambar\Services\RajaGambarConfigService;

class RajaGambarUploadExt extends FileUpload
{
    protected RajaGambarConfigService $configService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->configService = app(RajaGambarConfigService::class);
        
        // Set default configuration from RajaGambar config
        $this->disk($this->configService->getStorageDisk());
        $this->maxSize($this->configService->getMaxFileSize());
        $this->maxFiles($this->configService->getMaxFiles());
        $this->acceptedFileTypes($this->configService->getAcceptedFileTypes());
    }

    /**
     * Set directory using config service
     */
    public function rajaDirectory(string $type = 'images'): static
    {
        $fullPath = $this->configService->getFullUploadPath($type);
        return $this->directory($fullPath);
    }

    /**
     * Set directory for images
     */
    public function imagesDirectory(): static
    {
        return $this->rajaDirectory('images');
    }

    /**
     * Set directory for gallery
     */
    public function galleryDirectory(): static
    {
        return $this->rajaDirectory('gallery');
    }

    /**
     * Set directory for products
     */
    public function productsDirectory(): static
    {
        return $this->rajaDirectory('products');
    }

    /**
     * Set directory for banners
     */
    public function bannersDirectory(): static
    {
        return $this->rajaDirectory('banners');
    }

    /**
     * Set directory for avatars
     */
    public function avatarsDirectory(): static
    {
        return $this->rajaDirectory('avatars')
            ->maxFiles(1)
            ->imageEditor()
            ->circleCropper();
    }

    /**
     * Set directory for thumbnails
     */
    public function thumbnailsDirectory(): static
    {
        return $this->rajaDirectory('thumbnails');
    }

    /**
     * Enable WebP conversion if configured
     */
    public function enableWebp(): static
    {
        if ($this->configService->isWebpEnabled()) {
            $this->imageEditor()
                ->imageEditorMode(2); // Enable WebP conversion
        }
        
        return $this;
    }

    /**
     * Enable thumbnail generation if configured
     */
    public function enableThumbnails(): static
    {
        if ($this->configService->isThumbnailsEnabled()) {
            // Add thumbnail generation logic here
            // This would integrate with your thumbnail service
        }
        
        return $this;
    }

    /**
     * Apply image optimization settings
     */
    public function optimizeImages(): static
    {
        if ($this->configService->isOptimizationEnabled()) {
            $this->imageEditor()
                ->imageEditorAspectRatios(['16:9', '4:3', '1:1']);
        }
        
        return $this;
    }

    /**
     * Set up for avatar upload
     */
    public function avatarUpload(): static
    {
        return $this->avatarsDirectory()
            ->enableWebp()
            ->optimizeImages();
    }

    /**
     * Set up for gallery upload
     */
    public function galleryUpload(): static
    {
        return $this->galleryDirectory()
            ->multiple()
            ->maxFiles(20)
            ->enableWebp()
            ->enableThumbnails()
            ->reorderable();
    }

    /**
     * Set up for product upload
     */
    public function productUpload(): static
    {
        return $this->productsDirectory()
            ->multiple()
            ->maxFiles(10)
            ->enableWebp()
            ->enableThumbnails()
            ->optimizeImages();
    }

    /**
     * Set up for banner upload
     */
    public function bannerUpload(): static
    {
        return $this->bannersDirectory()
            ->maxFiles(1)
            ->enableWebp()
            ->optimizeImages()
            ->imageEditor()
            ->imageEditorAspectRatios(['16:9', '21:9']);
    }

    /**
     * Get file URL using config service
     */
    public function getFileUrl(string $filePath): string
    {
        return $this->configService->getFileUrl($filePath);
    }

    /**
     * Apply custom directory with specific settings
     */
    public function customDirectory(string $directory, array $settings = []): static
    {
        $this->rajaDirectory($directory);
        
        if (isset($settings['max_files'])) {
            $this->maxFiles($settings['max_files']);
        }
        
        if (isset($settings['multiple']) && $settings['multiple']) {
            $this->multiple();
        }
        
        if (isset($settings['webp']) && $settings['webp']) {
            $this->enableWebp();
        }
        
        if (isset($settings['thumbnails']) && $settings['thumbnails']) {
            $this->enableThumbnails();
        }
        
        if (isset($settings['optimize']) && $settings['optimize']) {
            $this->optimizeImages();
        }
        
        if (isset($settings['image_editor']) && $settings['image_editor']) {
            $this->imageEditor();
        }
        
        if (isset($settings['aspect_ratios'])) {
            $this->imageEditorAspectRatios($settings['aspect_ratios']);
        }
        
        return $this;
    }

    /**
     * Quick setup methods for common use cases
     */
    public function singleImage(string $directory = 'images'): static
    {
        return $this->rajaDirectory($directory)
            ->maxFiles(1)
            ->enableWebp()
            ->optimizeImages();
    }

    public function multipleImages(string $directory = 'gallery', int $maxFiles = 10): static
    {
        return $this->rajaDirectory($directory)
            ->multiple()
            ->maxFiles($maxFiles)
            ->enableWebp()
            ->enableThumbnails()
            ->reorderable();
    }

    public function heroImage(string $directory = 'banners'): static
    {
        return $this->rajaDirectory($directory)
            ->maxFiles(1)
            ->enableWebp()
            ->optimizeImages()
            ->imageEditor()
            ->imageEditorAspectRatios(['16:9', '21:9', '2:1']);
    }
}
