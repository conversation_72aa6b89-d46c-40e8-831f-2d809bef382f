<?php

namespace Modules\RajaGambar\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver as GdDriver;
use Intervention\Image\Drivers\Imagick\Driver as ImagickDriver;
use Intervention\Image\Interfaces\ImageInterface;
use Intervention\Image\Geometry\Factories\RectangleFactory;
use Exception;

class InterventionImageService
{
    protected ImageManager $manager;
    protected array $config;

    public function __construct()
    {
        $this->config = Config::get('rajagambar', []);
        $this->initializeImageManager();
    }

    /**
     * Initialize Image Manager with configured driver
     */
    protected function initializeImageManager(): void
    {
        $driver = $this->getImageDriver();
        
        $this->manager = new ImageManager(
            $driver === 'imagick' ? new ImagickDriver() : new GdDriver()
        );
    }

    /**
     * Get image driver from config
     */
    public function getImageDriver(): string
    {
        return $this->config['image']['driver'] ?? 'gd';
    }

    /**
     * Get image quality from config
     */
    public function getImageQuality(): int
    {
        return $this->config['image']['quality'] ?? 85;
    }

    /**
     * Check if auto orientation is enabled
     */
    public function isAutoOrientEnabled(): bool
    {
        return $this->config['image']['auto_orient'] ?? true;
    }

    /**
     * Check if metadata should be stripped
     */
    public function shouldStripMetadata(): bool
    {
        return $this->config['image']['strip_metadata'] ?? true;
    }

    /**
     * Get maximum image width
     */
    public function getMaxWidth(): ?int
    {
        return $this->config['image']['max_width'] ?? null;
    }

    /**
     * Get maximum image height
     */
    public function getMaxHeight(): ?int
    {
        return $this->config['image']['max_height'] ?? null;
    }

    /**
     * Get supported image formats
     */
    public function getSupportedFormats(): array
    {
        return $this->config['image']['supported_formats'] ?? ['jpeg', 'jpg', 'png', 'gif', 'webp'];
    }

    /**
     * Check if WebP conversion is enabled
     */
    public function isWebpEnabled(): bool
    {
        return $this->config['image']['webp']['enabled'] ?? true;
    }

    /**
     * Get WebP quality
     */
    public function getWebpQuality(): int
    {
        return $this->config['image']['webp']['quality'] ?? 80;
    }

    /**
     * Load image from file path
     */
    public function loadImage(string $path): ImageInterface
    {
        // Log file info for debugging
        Log::info("Loading image", [
            'path' => $path,
            'fileExists' => file_exists($path),
            'fileSize' => file_exists($path) ? filesize($path) : 'N/A',
            'mimeType' => file_exists($path) ? mime_content_type($path) : 'N/A',
            'isReadable' => is_readable($path)
        ]);

        try {
            return $this->manager->read($path);
        } catch (\Exception $e) {
            Log::error("Failed to load image", [
                'path' => $path,
                'error' => $e->getMessage(),
                'fileExists' => file_exists($path),
                'fileSize' => file_exists($path) ? filesize($path) : 'N/A'
            ]);
            throw $e;
        }
    }

    /**
     * Load image from binary data
     */
    public function loadFromBinary(string $data): ImageInterface
    {
        return $this->manager->read($data);
    }

    /**
     * Resize image with constraints
     */
    public function resize(ImageInterface $image, ?int $width = null, ?int $height = null, bool $aspectRatio = true): ImageInterface
    {
        if ($width || $height) {
            if ($aspectRatio) {
                $image = $image->scale($width, $height);
            } else {
                $image = $image->resize($width, $height);
            }
        }

        return $image;
    }

    /**
     * Crop image to specific dimensions
     */
    public function crop(ImageInterface $image, int $width, int $height, ?int $x = null, ?int $y = null): ImageInterface
    {
        if ($x !== null && $y !== null) {
            return $image->crop($width, $height, $x, $y);
        }

        return $image->cover($width, $height);
    }

    /**
     * Apply quality settings to image
     */
    public function applyQuality(ImageInterface $image, ?int $quality = null): ImageInterface
    {
        $quality = $quality ?? $this->getImageQuality();

        // encodeByMediaType returns EncodedImage, we need to return the original image
        // Quality will be applied when saving
        return $image;
    }

    /**
     * Convert image to WebP format
     */
    public function convertToWebp(ImageInterface $image, ?int $quality = null): ImageInterface
    {
        $quality = $quality ?? $this->getWebpQuality();
        
        return $image->toWebp($quality);
    }

    /**
     * Process image with all configured settings
     */
    public function processImage(string $inputPath, string $outputPath, array $options = []): bool
    {
        try {
            $image = $this->loadImage($inputPath);

            // Apply auto orientation if enabled
            if ($this->isAutoOrientEnabled()) {
                $image = $image->orient();
            }

            // Apply resize constraints if configured
            $maxWidth = $options['max_width'] ?? $this->getMaxWidth();
            $maxHeight = $options['max_height'] ?? $this->getMaxHeight();
            
            if ($maxWidth || $maxHeight) {
                $image = $this->resize($image, $maxWidth, $maxHeight);
            }

            // Apply custom resize if specified
            if (isset($options['width']) || isset($options['height'])) {
                $image = $this->resize(
                    $image, 
                    $options['width'] ?? null, 
                    $options['height'] ?? null,
                    $options['aspect_ratio'] ?? true
                );
            }

            // Apply crop if specified
            if (isset($options['crop'])) {
                $crop = $options['crop'];
                $image = $this->crop(
                    $image,
                    $crop['width'],
                    $crop['height'],
                    $crop['x'] ?? null,
                    $crop['y'] ?? null
                );
            }

            // Save processed image with quality
            $quality = $options['quality'] ?? $this->getImageQuality();
            $image->encodeByMediaType(quality: $quality)->save($outputPath);

            return true;
        } catch (Exception $e) {
            throw new Exception("Failed to process image: " . $e->getMessage());
        }
    }

    /**
     * Create thumbnail from image
     */
    public function createThumbnail(string $inputPath, string $outputPath, int $width, int $height, array $options = []): bool
    {
        try {
            $image = $this->loadImage($inputPath);

            // Apply auto orientation if enabled
            if ($this->isAutoOrientEnabled()) {
                $image = $image->orient();
            }

            // Create thumbnail using cover (crop to fit)
            $fit = $options['fit'] ?? 'cover';

            if ($fit === 'cover') {
                $image = $image->cover($width, $height);
            } elseif ($fit === 'contain') {
                $image = $image->scale($width, $height);
            } elseif ($fit === 'fill') {
                $image = $image->resize($width, $height);
            } else {
                $image = $image->scale($width, $height);
            }

            // Save thumbnail with quality
            $quality = $options['quality'] ?? $this->getImageQuality();
            $image->encodeByMediaType(quality: $quality)->save($outputPath);

            return true;
        } catch (Exception $e) {
            throw new Exception("Failed to create thumbnail: " . $e->getMessage());
        }
    }

    /**
     * Create percentage-based thumbnail
     */
    public function createPercentageThumbnail(string $inputPath, string $outputPath, int $percentage, array $options = []): bool
    {
        try {
            $image = $this->loadImage($inputPath);

            // Apply auto orientation if enabled
            if ($this->isAutoOrientEnabled()) {
                $image = $image->orient();
            }

            // Calculate new dimensions based on percentage
            $originalWidth = $image->width();
            $originalHeight = $image->height();

            $newWidth = (int) ($originalWidth * ($percentage / 100));
            $newHeight = (int) ($originalHeight * ($percentage / 100));

            // Resize image
            $image = $image->scale($newWidth, $newHeight);

            // Save thumbnail with quality
            $quality = $options['quality'] ?? $this->getImageQuality();
            $image->encodeByMediaType(quality: $quality)->save($outputPath);

            return true;
        } catch (Exception $e) {
            throw new Exception("Failed to create percentage thumbnail: " . $e->getMessage());
        }
    }

    /**
     * Get image information
     */
    public function getImageInfo(string $path): array
    {
        try {
            $image = $this->loadImage($path);
            
            return [
                'width' => $image->width(),
                'height' => $image->height(),
                'mime_type' => $image->origin()->mediaType(),
                'file_size' => filesize($path),
                'format' => pathinfo($path, PATHINFO_EXTENSION),
            ];
        } catch (Exception $e) {
            throw new Exception("Failed to get image info: " . $e->getMessage());
        }
    }

    /**
     * Validate if file is a supported image format
     */
    public function isValidImageFormat(string $path): bool
    {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        return in_array($extension, $this->getSupportedFormats());
    }

    /**
     * Get storage path for uploads
     */
    public function getStoragePath(): string
    {
        return $this->config['storage']['path'] ?? 'uploads';
    }

    /**
     * Get storage disk
     */
    public function getStorageDisk(): string
    {
        return $this->config['storage']['disk'] ?? 'public';
    }
}
