<?php

namespace Modules\RajaGambar\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Exception;

class ThumbnailService
{
    protected InterventionImageService $imageService;
    protected array $config;
    protected array $thumbnailConfig;

    public function __construct(InterventionImageService $imageService)
    {
        $this->imageService = $imageService;
        $this->config = Config::get('rajagambar', []);
        $this->thumbnailConfig = $this->config['thumbnails'] ?? [];
    }

    /**
     * Check if thumbnails are enabled
     */
    public function isEnabled(): bool
    {
        return $this->thumbnailConfig['enabled'] ?? true;
    }

    /**
     * Get thumbnail directory
     */
    public function getThumbnailDirectory(): string
    {
        return $this->thumbnailConfig['directory'] ?? 'thumbnails';
    }

    /**
     * Get thumbnail prefix
     */
    public function getThumbnailPrefix(): string
    {
        return $this->thumbnailConfig['prefix'] ?? '';
    }

    /**
     * Get thumbnail suffix
     */
    public function getThumbnailSuffix(): string
    {
        return $this->thumbnailConfig['suffix'] ?? '_th';
    }

    /**
     * Get thumbnail sizes configuration
     */
    public function getThumbnailSizes(): array
    {
        return $this->thumbnailConfig['sizes'] ?? [];
    }

    /**
     * Generate all configured thumbnails for an image
     */
    public function generateThumbnails(string $originalPath, string $baseDirectory = ''): array
    {
        if (!$this->isEnabled()) {
            return [];
        }

        $generatedThumbnails = [];
        $sizes = $this->getThumbnailSizes();

        foreach ($sizes as $sizeName => $sizeConfig) {
            if (!($sizeConfig['enabled'] ?? true)) {
                continue;
            }

            try {
                $thumbnailPath = $this->generateSingleThumbnail($originalPath, $sizeName, $sizeConfig, $baseDirectory);
                if ($thumbnailPath) {
                    $generatedThumbnails[$sizeName] = $thumbnailPath;
                }
            } catch (Exception $e) {
                // Log error but continue with other thumbnails
                \Log::error("Failed to generate thumbnail '{$sizeName}': " . $e->getMessage());
            }
        }

        return $generatedThumbnails;
    }

    /**
     * Generate single thumbnail
     */
    public function generateSingleThumbnail(string $originalPath, string $sizeName, array $sizeConfig, string $baseDirectory = ''): ?string
    {
        if (!$this->isEnabled() || !($sizeConfig['enabled'] ?? true)) {
            return null;
        }

        // Get original file info
        $pathInfo = pathinfo($originalPath);
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];

        // Create thumbnail filename
        $thumbnailFilename = $this->createThumbnailFilename($filename, $sizeName, $extension);
        
        // Create thumbnail directory path
        $thumbnailDir = $this->createThumbnailDirectoryPath($baseDirectory);
        
        // Full thumbnail path
        $thumbnailPath = $thumbnailDir . '/' . $thumbnailFilename;

        // Ensure thumbnail directory exists
        $fullThumbnailDir = storage_path($thumbnailDir);
        if (!File::exists($fullThumbnailDir)) {
            File::makeDirectory($fullThumbnailDir, 0755, true);
        }

        // Full paths for processing
        // Check if originalPath is already a full path (for temporary files)
        if (str_starts_with($originalPath, '/') || str_contains($originalPath, ':')) {
            // Already a full path (temporary file or absolute path)
            $fullOriginalPath = $originalPath;
        } else {
            // Relative path, add storage prefix
            $fullOriginalPath = storage_path($originalPath);
        }

        $fullThumbnailPath = storage_path($thumbnailPath);

        // Generate thumbnail based on type
        if (isset($sizeConfig['percentage'])) {
            // Percentage-based thumbnail
            $success = $this->imageService->createPercentageThumbnail(
                $fullOriginalPath,
                $fullThumbnailPath,
                $sizeConfig['percentage'],
                [
                    'quality' => $sizeConfig['quality'] ?? 80
                ]
            );
        } else {
            // Fixed size thumbnail
            $success = $this->imageService->createThumbnail(
                $fullOriginalPath,
                $fullThumbnailPath,
                $sizeConfig['width'] ?? 150,
                $sizeConfig['height'] ?? 150,
                [
                    'fit' => $sizeConfig['fit'] ?? 'cover',
                    'quality' => $sizeConfig['quality'] ?? 80
                ]
            );
        }

        // Convert to WebP if enabled
        if ($success && $this->shouldConvertToWebp()) {
            $webpPath = $this->convertThumbnailToWebp($fullThumbnailPath, $sizeConfig);
            if ($webpPath) {
                // Remove original thumbnail and use WebP
                File::delete($fullThumbnailPath);
                $thumbnailPath = str_replace(storage_path(''), '', $webpPath);
            }
        }

        return $success ? $thumbnailPath : null;
    }

    /**
     * Create thumbnail filename
     */
    protected function createThumbnailFilename(string $filename, string $sizeName, string $extension): string
    {
        $prefix = $this->getThumbnailPrefix();
        $suffix = $this->getThumbnailSuffix();
        
        // For percentage-based thumbnails, use the size name as suffix
        if (str_ends_with($sizeName, 'p')) {
            $suffix = '_' . $sizeName;
        } else {
            $suffix = $suffix . '_' . $sizeName;
        }

        // Use WebP extension if conversion is enabled
        if ($this->shouldConvertToWebp()) {
            $extension = 'webp';
        }

        return $prefix . $filename . $suffix . '.' . $extension;
    }

    /**
     * Create thumbnail directory path
     */
    protected function createThumbnailDirectoryPath(string $baseDirectory = ''): string
    {
        $thumbnailDir = $this->getThumbnailDirectory();
        
        if ($baseDirectory) {
            return $baseDirectory . '/' . $thumbnailDir;
        }
        
        return $this->config['storage']['path'] . '/' . $thumbnailDir;
    }

    /**
     * Check if WebP conversion should be applied
     */
    protected function shouldConvertToWebp(): bool
    {
        return $this->imageService->isWebpEnabled();
    }

    /**
     * Convert thumbnail to WebP format
     */
    protected function convertThumbnailToWebp(string $thumbnailPath, array $sizeConfig): ?string
    {
        try {
            $pathInfo = pathinfo($thumbnailPath);
            $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
            
            $image = $this->imageService->loadImage($thumbnailPath);
            $webpQuality = $sizeConfig['quality'] ?? $this->imageService->getWebpQuality();

            $success = $this->imageService->convertToWebp($image, $webpPath, $webpQuality);
            if (!$success) {
                return null;
            }
            
            return $webpPath;
        } catch (Exception $e) {
            \Log::error("Failed to convert thumbnail to WebP: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete all thumbnails for a file
     */
    public function deleteThumbnails(string $originalPath, string $baseDirectory = ''): bool
    {
        $pathInfo = pathinfo($originalPath);
        $filename = $pathInfo['filename'];
        
        $thumbnailDir = $this->createThumbnailDirectoryPath($baseDirectory);
        $fullThumbnailDir = storage_path($thumbnailDir);
        
        if (!File::exists($fullThumbnailDir)) {
            return true; // No thumbnails to delete
        }

        $deleted = true;
        $sizes = $this->getThumbnailSizes();

        foreach ($sizes as $sizeName => $sizeConfig) {
            try {
                // Delete both original and WebP versions
                $extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                
                foreach ($extensions as $ext) {
                    $thumbnailFilename = $this->createThumbnailFilename($filename, $sizeName, $ext);
                    $thumbnailPath = $fullThumbnailDir . '/' . $thumbnailFilename;
                    
                    if (File::exists($thumbnailPath)) {
                        File::delete($thumbnailPath);
                    }
                }
            } catch (Exception $e) {
                \Log::error("Failed to delete thumbnail '{$sizeName}': " . $e->getMessage());
                $deleted = false;
            }
        }

        return $deleted;
    }

    /**
     * Get thumbnail URL
     */
    public function getThumbnailUrl(string $originalPath, string $sizeName, string $baseDirectory = ''): ?string
    {
        $pathInfo = pathinfo($originalPath);
        $filename = $pathInfo['filename'];
        $extension = $this->shouldConvertToWebp() ? 'webp' : $pathInfo['extension'];
        
        $thumbnailFilename = $this->createThumbnailFilename($filename, $sizeName, $extension);
        $thumbnailDir = $this->createThumbnailDirectoryPath($baseDirectory);
        
        $thumbnailPath = $thumbnailDir . '/' . $thumbnailFilename;
        $fullThumbnailPath = storage_path($thumbnailPath);
        
        if (File::exists($fullThumbnailPath)) {
            return asset('storage/' . $thumbnailPath);
        }
        
        return null;
    }

    /**
     * Check if thumbnail exists
     */
    public function thumbnailExists(string $originalPath, string $sizeName, string $baseDirectory = ''): bool
    {
        return $this->getThumbnailUrl($originalPath, $sizeName, $baseDirectory) !== null;
    }
}
