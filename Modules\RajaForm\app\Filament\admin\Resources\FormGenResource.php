<?php

namespace Modules\RajaForm\Filament\admin\Resources;


use Abdelhamid<PERSON><PERSON><PERSON>ouni\FilamentMonacoEditor\MonacoEditor;
use Modules\RajaForm\Filament\admin\Resources\FormGenResource\Pages;
use Modules\RajaForm\Filament\admin\Resources\FormGenResource\Pages\CreateFormGen;
use Modules\RajaForm\Filament\admin\Resources\FormGenResource\Pages\EditFormGen;
use Modules\RajaForm\Filament\admin\Resources\FormGenResource\Pages\ListFormGens;
use Modules\RajaForm\Filament\admin\Resources\FormGenResource\RelationManagers;
use Modules\RajaForm\Models\FormGen;
use Filament\Actions\EditAction;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

use Riodwanto\FilamentAceEditor\AceEditor;
use ValentinMorice\FilamentJsonColumn\JsonColumn;

class FormGenResource extends Resource
{
    protected static ?string $model = FormGen::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'Form Generator';

    protected static ?string $modelLabel = 'Form Generator';

    protected static ?string $pluralModelLabel = 'Form Generator';

    protected static ?string $navigationGroup = 'System';
    protected static ?string $slug = 'form-generator';
    protected static ?string $createActionLabel = 'Tambah Form';
    protected static bool $shouldRegisterNavigation = true;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->schema([
                        // Kolom 1 (70%): Form Designer
                        Grid::make()



                            ->schema([

                                Section::make('Form Designer')
                                    ->description('untuk mendesain form dan fields')
                                    ->collapsible()

                                    ->schema([
                                        // Repeater untuk mengelola field
                                        Repeater::make('value')
                                            ->label('Field Form')
                                            ->grid(1)
                                            ->columns(4)
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Set $set, $state) {
                                                // Auto-generate JSON ketika repeater berubah
                                                $jsonData = self::generateJsonFromRepeater($state ?? []);
                                                $jsonString = json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                                                $set('jsonpreview', $jsonString);
                                            })
                                            ->schema([
                                                // 1. Jenis field
                                                Select::make('type')
                                                    ->options([
                                                        'textinput' => 'TextInput',
                                                        'textarea' => 'Textarea',
                                                        'select' => 'Select Dropdown',
                                                        'radio' => 'Radio Button',
                                                        'checkbox' => 'Checkbox',
                                                        'fileupload' => 'File Upload',
                                                        'keyvalue' => 'Key-Value',
                                                        'tagsinput' => 'Tags Input',
                                                        'rajagambar' => 'RajaGambar (Image Upload)',
                                                        'placeholder' => 'Placeholder',
                                                        'viewfield' => 'View Field',
                                                    ])
                                                    ->label('Jenis Input')
                                                    ->placeholder('Pilih jenis')
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                    ->required()
                                                    ->columnSpan(1),

                                                // 2. Nama field (untuk JSON key)
                                                TextInput::make('name')
                                                    ->visible(fn($get) => $get('type') != '')
                                                    ->label('Nama Field')
                                                    ->placeholder('nama_field')
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                    ->required()
                                                    ->helperText('Nama field untuk JSON key')
                                                    ->columnSpan(1),





                                                // Placeholder
                                                TextInput::make('placeholder')
                                                    ->label('Placeholder')
                                                    ->placeholder('Placeholder text')
                                                    ->visible(fn($get) => in_array($get('type'), ['textinput', 'textarea', 'rajagambar']))
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                    ->columnSpan(1),

                                                // Options untuk select/radio
                                                Textarea::make('options')
                                                    ->label('Options')
                                                    ->placeholder('option1,option2,option3')
                                                    ->helperText('Pisahkan dengan koma')
                                                    ->visible(fn($get) => in_array($get('type'), ['select', 'radio']))
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                    ->columnSpan(1),

                                                // Helper text (tooltip)
                                                TextInput::make('helper_text')
                                                    ->label('Pesan bantuan (Tooltip)')
                                                    ->visible(fn($get) => $get('type') != '')
                                                    ->placeholder('Teks bantuan')
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                    ->columnSpan(1),






                                                /////// PENGATURAN LANJUTAN

                                                Section::make('Pengaturan lanjutan')
                                                    ->visible(fn($get) => $get('type') != '')
                                                    ->collapsible()
                                                    ->collapsed()
                                                    ->columns(4)
                                                    ->columnSpanFull()
                                                    ->schema([
                                                        // Label field
                                                        Select::make('field_label')
                                                            ->options([
                                                                'auto' => 'Auto (dari nama field)',
                                                                'custom' => 'Custom label',
                                                                'false' => 'Tanpa label',
                                                            ])
                                                            ->default('auto')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->label('Label')
                                                            ->columnSpan(1),

                                                        // Custom label (muncul jika pilih custom)
                                                        TextInput::make('label')
                                                            ->label('Custom Label')
                                                            ->placeholder('Label custom')
                                                            ->visible(fn($get) => $get('field_label') === 'custom')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpanFull(),

                                                        // Column span
                                                        Select::make('columnspan')
                                                            ->label('Lebar Kolom')
                                                            ->options([
                                                                '1' => '1 - Sempit',
                                                                '2' => '2 - Sedang',
                                                                '3' => '3 - Lebar',
                                                                'full' => 'Full - Penuh'
                                                            ])
                                                            ->default('1')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Default value
                                                        Textarea::make('default')
                                                            ->label('Nilai Default')
                                                            ->placeholder('Nilai awal field')
                                                            ->rows(2)
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),

                                                        // === PROPERTI KHUSUS TEXTINPUT ===

                                                        // Prefix
                                                        TextInput::make('prefix')
                                                            ->label('Prefix')
                                                            ->placeholder('Contoh: Rp, $')
                                                            ->visible(fn($get) => in_array($get('type'), ['textinput', 'select']))
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Suffix
                                                        TextInput::make('suffix')
                                                            ->label('Suffix')
                                                            ->placeholder('Contoh: kg, cm')
                                                            ->visible(fn($get) => in_array($get('type'), ['textinput', 'select']))
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Mask untuk TextInput
                                                        TextInput::make('mask')
                                                            ->label('Input Mask')
                                                            ->placeholder('Contoh: 999-999-9999')
                                                            ->visible(fn($get) => $get('type') === 'textinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),

                                                        // === PROPERTI KHUSUS TEXTAREA ===

                                                        // Lebar textarea (cols)
                                                        TextInput::make('lebar')
                                                            ->label('Lebar (kolom)')
                                                            ->placeholder('Contoh: 20')
                                                            ->numeric()
                                                            ->visible(fn($get) => $get('type') === 'textarea')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Tinggi textarea (rows)
                                                        TextInput::make('tinggi')
                                                            ->label('Tinggi (baris)')
                                                            ->placeholder('Contoh: 5')
                                                            ->numeric()
                                                            ->visible(fn($get) => $get('type') === 'textarea')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // === PROPERTI KHUSUS VIEWFIELD ===

                                                        // View path
                                                        TextInput::make('view')
                                                            ->label('Path View')
                                                            ->placeholder('Contoh: components.custom-field')
                                                            ->visible(fn($get) => $get('type') === 'viewfield')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),

                                                        // View data (JSON)
                                                        Textarea::make('data')
                                                            ->label('Data View (JSON)')
                                                            ->placeholder('{"key": "value"}')
                                                            ->visible(fn($get) => $get('type') === 'viewfield')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),

                                                        // === PROPERTI KHUSUS FILEUPLOAD ===

                                                        // Directory
                                                        TextInput::make('directory')
                                                            ->label('Directory')
                                                            ->placeholder('Contoh: uploads/images')
                                                            ->visible(fn($get) => $get('type') === 'fileupload')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),

                                                        // File prefix
                                                        TextInput::make('file_prefix')
                                                            ->label('Prefix Nama File')
                                                            ->placeholder('Contoh: img_')
                                                            ->visible(fn($get) => $get('type') === 'fileupload')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Resize width
                                                        TextInput::make('resizewidth')
                                                            ->label('Resize Width')
                                                            ->placeholder('1920')
                                                            ->numeric()
                                                            ->visible(fn($get) => $get('type') === 'fileupload')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Resize height
                                                        TextInput::make('resizeheight')
                                                            ->label('Resize Height')
                                                            ->placeholder('1080')
                                                            ->numeric()
                                                            ->visible(fn($get) => $get('type') === 'fileupload')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // === TOGGLE PROPERTIES ===

                                                        // Disabled
                                                        Checkbox::make('disabled')
                                                            ->label('Disabled')
                                                            ->default(false)
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Readonly
                                                        Checkbox::make('readonly')
                                                            ->label('Readonly')
                                                            ->default(false)
                                                            ->visible(fn($get) => in_array($get('type'), ['textinput', 'textarea']))
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Numeric (untuk TextInput)
                                                        Checkbox::make('is_numeric')
                                                            ->label('Numeric')
                                                            ->default(false)
                                                            ->visible(fn($get) => $get('type') === 'textinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // 4. Required
                                                        Checkbox::make('required')
                                                            ->label('Wajib diisi ?')
                                                            ->visible(fn($get) => $get('type') != '')

                                                            // ->default(true)
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Inline (untuk Radio)
                                                        Checkbox::make('inline')
                                                            ->label('Inline')
                                                            ->default(false)
                                                            ->visible(fn($get) => $get('type') === 'radio')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Autosize (untuk Textarea)
                                                        Checkbox::make('autosize')
                                                            ->label('Autosize')
                                                            ->default(false)
                                                            ->visible(fn($get) => $get('type') === 'textarea')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Image Editor (untuk FileUpload)
                                                        Checkbox::make('editor')
                                                            ->label('Image Editor')
                                                            ->default(false)
                                                            ->visible(fn($get) => $get('type') === 'fileupload')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // === PROPERTI KHUSUS KEYVALUE ===

                                                        // Key Label
                                                        TextInput::make('key_label')
                                                            ->label('Label Kunci')
                                                            ->placeholder('Contoh: Properti')
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Value Label
                                                        TextInput::make('value_label')
                                                            ->label('Label Nilai')
                                                            ->placeholder('Contoh: Nilai')
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Key Placeholder
                                                        TextInput::make('key_placeholder')
                                                            ->label('Placeholder Kunci')
                                                            ->placeholder('Masukkan kunci')
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Value Placeholder
                                                        TextInput::make('value_placeholder')
                                                            ->label('Placeholder Nilai')
                                                            ->placeholder('Masukkan nilai')
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Add Action Label
                                                        TextInput::make('add_action_label')
                                                            ->label('Label Tombol Tambah')
                                                            ->placeholder('Tambah Item')
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),

                                                        // KeyValue Toggles
                                                        Checkbox::make('kv_addable')
                                                            ->label('Dapat Menambah')
                                                            ->default(true)
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        Checkbox::make('kv_deletable')
                                                            ->label('Dapat Menghapus')
                                                            ->default(true)
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        Checkbox::make('kv_editable_keys')
                                                            ->label('Kunci Dapat Diedit')
                                                            ->default(true)
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        Checkbox::make('kv_editable_values')
                                                            ->label('Nilai Dapat Diedit')
                                                            ->default(true)
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        Checkbox::make('kv_reorderable')
                                                            ->label('Dapat Diurutkan')
                                                            ->default(false)
                                                            ->visible(fn($get) => $get('type') === 'keyvalue')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // === PROPERTI KHUSUS TAGSINPUT ===

                                                        // Separator
                                                        TextInput::make('separator')
                                                            ->label('Separator')
                                                            ->placeholder('Contoh: ,')
                                                            ->helperText('Kosongkan untuk format JSON array')
                                                            ->visible(fn($get) => $get('type') === 'tagsinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Suggestions
                                                        Textarea::make('suggestions')
                                                            ->label('Suggestions')
                                                            ->placeholder('tag1,tag2,tag3')
                                                            ->helperText('Pisahkan dengan koma untuk autocomplete')
                                                            ->visible(fn($get) => $get('type') === 'tagsinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),

                                                        // Split Keys
                                                        TextInput::make('split_keys')
                                                            ->label('Split Keys')
                                                            ->placeholder('Tab, ')
                                                            ->helperText('Tombol untuk membuat tag baru')
                                                            ->visible(fn($get) => $get('type') === 'tagsinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Tag Prefix
                                                        TextInput::make('tag_prefix')
                                                            ->label('Tag Prefix')
                                                            ->placeholder('Contoh: #')
                                                            ->visible(fn($get) => $get('type') === 'tagsinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Tag Suffix
                                                        TextInput::make('tag_suffix')
                                                            ->label('Tag Suffix')
                                                            ->placeholder('Contoh: %')
                                                            ->visible(fn($get) => $get('type') === 'tagsinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Tag Color
                                                        Select::make('tag_color')
                                                            ->label('Warna Tag')
                                                            ->options([
                                                                'primary' => 'Primary',
                                                                'secondary' => 'Secondary',
                                                                'success' => 'Success',
                                                                'warning' => 'Warning',
                                                                'danger' => 'Danger',
                                                                'info' => 'Info',
                                                                'gray' => 'Gray',
                                                            ])
                                                            ->default('primary')
                                                            ->visible(fn($get) => $get('type') === 'tagsinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Tags Reorderable
                                                        Checkbox::make('tags_reorderable')
                                                            ->label('Tag Dapat Diurutkan')
                                                            ->default(false)
                                                            ->visible(fn($get) => $get('type') === 'tagsinput')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // === PROPERTI KHUSUS RAJAGAMBAR ===

                                                        // Collection
                                                        TextInput::make('collection')
                                                            ->label('Collection Media')
                                                            ->placeholder('Contoh: cms, gallery, products')
                                                            ->default('default')
                                                            ->visible(fn($get) => $get('type') === 'rajagambar')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),

                                                        // Directory Type
                                                        Select::make('directory_type')
                                                            ->label('Directory Type')
                                                            ->options([
                                                                'images' => 'Images',
                                                                'gallery' => 'Gallery',
                                                                'banner' => 'Banner',
                                                                'avatar' => 'Avatar',
                                                                'product' => 'Product',
                                                            ])
                                                            ->default('images')
                                                            ->visible(fn($get) => $get('type') === 'rajagambar')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Max Files
                                                        TextInput::make('max_files')
                                                            ->label('Max Files')
                                                            ->placeholder('1')
                                                            ->numeric()
                                                            ->default(1)
                                                            ->visible(fn($get) => $get('type') === 'rajagambar')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),

                                                        // Accepted Types
                                                        Textarea::make('accepted_types')
                                                            ->label('Tipe File yang Diterima')
                                                            ->placeholder('image/jpeg,image/png,image/gif,image/webp')
                                                            ->helperText('Pisahkan dengan koma')
                                                            ->rows(2)
                                                            ->visible(fn($get) => $get('type') === 'rajagambar')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(2),



                                                        // Multiple Selection
                                                        Checkbox::make('multiple')
                                                            ->label('Multiple Selection')
                                                            ->default(false)
                                                            ->helperText('Izinkan memilih beberapa gambar')
                                                            ->visible(fn($get) => $get('type') === 'rajagambar')
                                                            ->live(onBlur: true)
                                                            ->afterStateUpdated(fn(Get $get, Set $set) => self::updateJsonFromRepeater($get, $set))
                                                            ->columnSpan(1),



                                                    ]),
                                            ])
                                            ->addActionLabel('Tambah Field')
                                            ->reorderableWithButtons()
                                            ->collapsible()
                                            ->itemLabel(fn(array $state): ?string => $state['name'] ?? 'Field Baru'),
                                    ]),





                            ])
                            ->columnSpan(4),

                        // Kolom 2 (30%): Preview dan Controls
                        Section::make('Kontrol & Preview')
                            ->schema([

                                TextInput::make('key')
                                    ->unique(ignoreRecord: true)
                                    ->label('Kunci Form')
                                    ->helperText('Tidak boleh spasi dan karakter khusus')
                                    ->required(),


                                // MonacoEditor::make('value')
                                //     ->language('json')
                                // AceEditor::make('value')
                                // ->mode('json')
                                JsonColumn::make('jsonpreview')
                                    ->label('Preview json')
                                    ->viewerOnly()
                                    ->viewerHeight(500)
                                    ->live()
                                    ->reactive()
                                    ->afterStateHydrated(function (Get $get, Set $set, $state) {
                                        // Generate JSON saat form pertama kali dimuat
                                        if (empty($state)) {
                                            $repeaterData = $get('json') ?? [];
                                            if (!empty($repeaterData)) {
                                                $jsonData = self::generateJsonFromRepeater($repeaterData);
                                                $jsonString = json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                                                $set('jsonpreview', $jsonString);
                                            }
                                        }
                                    }),


                                    Grid::make()
                                    ->schema([

                                    ]),
                            ])
                            ->columnSpan(2),





                    ])
                    ->columns(6),
            ]);
    }

    /**
     * Helper function untuk update JSON dari repeater data
     */
    private static function updateJsonFromRepeater(Get $get, Set $set): void
    {
        $repeaterData = $get('../../value') ?? [];
        $jsonData = self::generateJsonFromRepeater($repeaterData);
        $jsonString = json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        $set('../../jsonpreview', $jsonString);
    }

    /**
     * Generate JSON dari data repeater - Format kompatibel dengan FieldBuilder6
     */
    public static function generateJsonFromRepeater(array $repeaterData): array
    {
        $jsonData = [];

        foreach ($repeaterData as $item) {
            $fieldData = [
                'type' => $item['type'] ?? 'textinput',
                'name' => $item['name'] ?? '',
            ];

            // Handle label berdasarkan pilihan - kompatibel dengan FieldBuilder6
            $fieldLabel = $item['field_label'] ?? 'auto';

            if ($fieldLabel === 'custom') {
                // Gunakan custom label
                $fieldData['label'] = $item['label'] ?? '';
            } elseif ($fieldLabel === 'false') {
                // Tanpa label
                $fieldData['label'] = false;
            } elseif ($fieldLabel === 'auto') {
                // Auto-generate label dari nama field (akan dihandle oleh FieldBuilder6)
                // Tidak perlu set label, biarkan FieldBuilder6 yang generate
            }

            // Hanya tambahkan required jika bukan placeholder atau viewfield
            if (!in_array($fieldData['type'], ['placeholder', 'viewfield'])) {
                if (isset($item['required'])) {
                    $fieldData['required'] = $item['required'];
                }
            }

            // Tambahkan properti lainnya sesuai format FieldBuilder6
            if (!empty($item['placeholder'])) {
                $fieldData['placeholder'] = $item['placeholder'];
            }

            if (!empty($item['helper_text'])) {
                $fieldData['tooltip'] = $item['helper_text']; // FieldBuilder6 menggunakan 'tooltip'
            }

            if (!empty($item['options'])) {
                $fieldData['options'] = $item['options'];
            }

            if (!empty($item['columnspan'])) {
                $fieldData['columnspan'] = $item['columnspan'];
            }

            // Tambahkan properti disabled jika ada
            if (isset($item['disabled']) && $item['disabled']) {
                $fieldData['disabled'] = true;
            }

            // Tambahkan properti default jika ada
            if (!empty($item['default'])) {
                $fieldData['default'] = $item['default'];
            }

            // Properti khusus untuk textinput
            if ($fieldData['type'] === 'textinput') {
                if (!empty($item['mask'])) {
                    $fieldData['mask'] = $item['mask'];
                }

                if (!empty($item['prefix'])) {
                    $fieldData['prefix'] = $item['prefix'];
                }

                if (!empty($item['suffix'])) {
                    $fieldData['suffix'] = $item['suffix'];
                }

                if (isset($item['is_numeric']) && $item['is_numeric']) {
                    $fieldData['is_numeric'] = true;
                }

                if (isset($item['readonly']) && $item['readonly']) {
                    $fieldData['readonly'] = true;
                }
            }

            // Properti khusus untuk textarea
            if ($fieldData['type'] === 'textarea') {
                if (!empty($item['tinggi'])) {
                    $fieldData['tinggi'] = (int) $item['tinggi'];
                }

                if (!empty($item['lebar'])) {
                    $fieldData['lebar'] = (int) $item['lebar'];
                }

                if (isset($item['autosize']) && $item['autosize']) {
                    $fieldData['autosize'] = true;
                }

                if (isset($item['readonly']) && $item['readonly']) {
                    $fieldData['readonly'] = true;
                }
            }

            // Properti khusus untuk select
            if ($fieldData['type'] === 'select') {
                if (!empty($item['prefix'])) {
                    $fieldData['prefix'] = $item['prefix'];
                }

                if (!empty($item['suffix'])) {
                    $fieldData['suffix'] = $item['suffix'];
                }
            }

            // Properti khusus untuk radio
            if ($fieldData['type'] === 'radio') {
                if (isset($item['inline']) && $item['inline']) {
                    $fieldData['inline'] = true;
                }
            }

            // Properti khusus untuk viewfield
            if ($fieldData['type'] === 'viewfield') {
                if (!empty($item['view'])) {
                    $fieldData['view'] = $item['view'];
                }

                if (!empty($item['data'])) {
                    // Coba parse JSON jika berupa string
                    if (is_string($item['data'])) {
                        $parsedData = json_decode($item['data'], true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($parsedData)) {
                            $fieldData['data'] = $parsedData;
                        } else {
                            $fieldData['data'] = ['content' => $item['data']];
                        }
                    } elseif (is_array($item['data'])) {
                        $fieldData['data'] = $item['data'];
                    }
                }
            }

            // Properti khusus untuk fileupload
            if ($fieldData['type'] === 'fileupload') {
                if (!empty($item['directory'])) {
                    $fieldData['directory'] = $item['directory'];
                }

                if (isset($item['editor']) && $item['editor']) {
                    $fieldData['editor'] = true;
                }

                if (!empty($item['resizewidth'])) {
                    $fieldData['resizewidth'] = (int) $item['resizewidth'];
                }

                if (!empty($item['resizeheight'])) {
                    $fieldData['resizeheight'] = (int) $item['resizeheight'];
                }

                if (!empty($item['file_prefix'])) {
                    $fieldData['file_prefix'] = $item['file_prefix'];
                }
            }

            // Properti khusus untuk keyvalue
            if ($fieldData['type'] === 'keyvalue') {
                if (!empty($item['key_label'])) {
                    $fieldData['key_label'] = $item['key_label'];
                }

                if (!empty($item['value_label'])) {
                    $fieldData['value_label'] = $item['value_label'];
                }

                if (!empty($item['key_placeholder'])) {
                    $fieldData['key_placeholder'] = $item['key_placeholder'];
                }

                if (!empty($item['value_placeholder'])) {
                    $fieldData['value_placeholder'] = $item['value_placeholder'];
                }

                if (!empty($item['add_action_label'])) {
                    $fieldData['add_action_label'] = $item['add_action_label'];
                }

                if (isset($item['kv_addable']) && !$item['kv_addable']) {
                    $fieldData['addable'] = false;
                }

                if (isset($item['kv_deletable']) && !$item['kv_deletable']) {
                    $fieldData['deletable'] = false;
                }

                if (isset($item['kv_editable_keys']) && !$item['kv_editable_keys']) {
                    $fieldData['editable_keys'] = false;
                }

                if (isset($item['kv_editable_values']) && !$item['kv_editable_values']) {
                    $fieldData['editable_values'] = false;
                }

                if (isset($item['kv_reorderable']) && $item['kv_reorderable']) {
                    $fieldData['reorderable'] = true;
                }
            }

            // Properti khusus untuk tagsinput
            if ($fieldData['type'] === 'tagsinput') {
                if (!empty($item['separator'])) {
                    $fieldData['separator'] = $item['separator'];
                }

                if (!empty($item['suggestions'])) {
                    $fieldData['suggestions'] = $item['suggestions'];
                }

                if (!empty($item['split_keys'])) {
                    $fieldData['split_keys'] = $item['split_keys'];
                }

                if (!empty($item['tag_prefix'])) {
                    $fieldData['tag_prefix'] = $item['tag_prefix'];
                }

                if (!empty($item['tag_suffix'])) {
                    $fieldData['tag_suffix'] = $item['tag_suffix'];
                }

                if (!empty($item['tag_color'])) {
                    $fieldData['color'] = $item['tag_color'];
                }

                if (isset($item['tags_reorderable']) && $item['tags_reorderable']) {
                    $fieldData['reorderable'] = true;
                }
            }

            // Properti khusus untuk rajagambar
            if ($fieldData['type'] === 'rajagambar') {
                if (!empty($item['collection'])) {
                    $fieldData['collection'] = $item['collection'];
                }

                if (!empty($item['directory_type'])) {
                    $fieldData['directory_type'] = $item['directory_type'];
                }

                if (!empty($item['max_files'])) {
                    $fieldData['max_files'] = (int) $item['max_files'];
                }

                if (!empty($item['accepted_types'])) {
                    // Convert comma-separated string to array
                    if (is_string($item['accepted_types'])) {
                        $types = array_map('trim', explode(',', $item['accepted_types']));
                        $fieldData['accepted_types'] = array_filter($types);
                    } else {
                        $fieldData['accepted_types'] = $item['accepted_types'];
                    }
                }

                if (isset($item['multiple']) && $item['multiple']) {
                    $fieldData['multiple'] = true;
                }
            }

            $jsonData[] = $fieldData;
        }

        return $jsonData;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label('Kunci Form')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('jenis')
                    ->label('Jenis')
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                
                    Tables\Actions\DeleteBulkAction::make(),
              
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFormGens::route('/'),
            'create' => Pages\CreateFormGen::route('/create'),
            'edit' => Pages\EditFormGen::route('/{record}/edit'),
        ];
    }
}
