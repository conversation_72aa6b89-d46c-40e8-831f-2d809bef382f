<?php

namespace Modules\RajaGambar\Traits;

use <PERSON><PERSON>les\RajaGambar\Services\ThumbnailService;
use Modules\RajaGambar\Services\InterventionImageService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Exception;

trait GeneratesThumbnails
{
    protected ?ThumbnailService $thumbnailService = null;
    protected bool $autoGenerateThumbnails = true;
    protected array $thumbnailSizes = [];
    protected bool $convertToWebp = true;
    protected bool $deleteOriginalAfterWebp = false;

    /**
     * Initialize thumbnail service
     */
    protected function initializeThumbnailService(): void
    {
        if (!$this->thumbnailService) {
            $imageService = app(InterventionImageService::class);
            $this->thumbnailService = new ThumbnailService($imageService);
        }
    }

    /**
     * Enable/disable automatic thumbnail generation
     */
    public function autoGenerateThumbnails(bool $enabled = true): static
    {
        $this->autoGenerateThumbnails = $enabled;
        return $this;
    }

    /**
     * Set custom thumbnail sizes
     */
    public function thumbnailSizes(array $sizes): static
    {
        $this->thumbnailSizes = $sizes;
        return $this;
    }

    /**
     * Enable/disable WebP conversion
     */
    public function convertToWebp(bool $enabled = true): static
    {
        $this->convertToWebp = $enabled;
        return $this;
    }

    /**
     * Enable/disable deleting original after WebP conversion
     */
    public function deleteOriginalAfterWebp(bool $enabled = false): static
    {
        $this->deleteOriginalAfterWebp = $enabled;
        return $this;
    }

    /**
     * Generate thumbnails for uploaded files
     */
    public function generateThumbnailsForFiles(array $filePaths, string $baseDirectory = ''): array
    {
        $this->initializeThumbnailService();
        
        if (!$this->autoGenerateThumbnails || !$this->thumbnailService->isEnabled()) {
            return [];
        }

        $allThumbnails = [];

        foreach ($filePaths as $filePath) {
            try {
                $thumbnails = $this->thumbnailService->generateThumbnails($filePath, $baseDirectory);
                $allThumbnails[$filePath] = $thumbnails;

                // Convert original to WebP if enabled
                if ($this->convertToWebp && $this->shouldConvertOriginalToWebp($filePath)) {
                    $webpPath = $this->convertOriginalToWebp($filePath);
                    if ($webpPath && $this->deleteOriginalAfterWebp) {
                        $this->deleteOriginalFile($filePath);
                    }
                }
            } catch (Exception $e) {
                \Log::error("Failed to generate thumbnails for {$filePath}: " . $e->getMessage());
            }
        }

        return $allThumbnails;
    }

    /**
     * Delete thumbnails for files
     */
    public function deleteThumbnailsForFiles(array $filePaths, string $baseDirectory = ''): bool
    {
        $this->initializeThumbnailService();
        
        $success = true;

        foreach ($filePaths as $filePath) {
            try {
                if (!$this->thumbnailService->deleteThumbnails($filePath, $baseDirectory)) {
                    $success = false;
                }
            } catch (Exception $e) {
                \Log::error("Failed to delete thumbnails for {$filePath}: " . $e->getMessage());
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Check if original file should be converted to WebP
     */
    protected function shouldConvertOriginalToWebp(string $filePath): bool
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        return in_array($extension, ['jpg', 'jpeg', 'png']) && $extension !== 'webp';
    }

    /**
     * Convert original file to WebP
     */
    protected function convertOriginalToWebp(string $filePath): ?string
    {
        try {
            $imageService = app(InterventionImageService::class);
            
            $pathInfo = pathinfo($filePath);
            $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
            
            $fullOriginalPath = storage_path('app/public/' . $filePath);
            $fullWebpPath = storage_path('app/public/' . $webpPath);
            
            $image = $imageService->loadImage($fullOriginalPath);
            $image = $imageService->convertToWebp($image);
            $image->save($fullWebpPath);
            
            return $webpPath;
        } catch (Exception $e) {
            \Log::error("Failed to convert original to WebP: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete original file
     */
    protected function deleteOriginalFile(string $filePath): bool
    {
        try {
            $fullPath = storage_path('app/public/' . $filePath);
            if (File::exists($fullPath)) {
                return File::delete($fullPath);
            }
            return true;
        } catch (Exception $e) {
            \Log::error("Failed to delete original file {$filePath}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get thumbnail URL for a file
     */
    public function getThumbnailUrl(string $filePath, string $sizeName, string $baseDirectory = ''): ?string
    {
        $this->initializeThumbnailService();
        return $this->thumbnailService->getThumbnailUrl($filePath, $sizeName, $baseDirectory);
    }

    /**
     * Check if thumbnail exists
     */
    public function thumbnailExists(string $filePath, string $sizeName, string $baseDirectory = ''): bool
    {
        $this->initializeThumbnailService();
        return $this->thumbnailService->thumbnailExists($filePath, $sizeName, $baseDirectory);
    }

    /**
     * Get all available thumbnail sizes
     */
    public function getAvailableThumbnailSizes(): array
    {
        $this->initializeThumbnailService();
        return array_keys($this->thumbnailService->getThumbnailSizes());
    }

    /**
     * Get thumbnail configuration
     */
    public function getThumbnailConfig(): array
    {
        $this->initializeThumbnailService();
        return $this->thumbnailService->getThumbnailSizes();
    }

    /**
     * Generate thumbnails on form submission
     * This method should be called after files are saved
     */
    public function afterSave(): void
    {
        if (!$this->autoGenerateThumbnails) {
            return;
        }

        // Get the current field value (file paths)
        $filePaths = $this->getState();
        
        if (empty($filePaths)) {
            return;
        }

        // Ensure it's an array
        if (!is_array($filePaths)) {
            $filePaths = [$filePaths];
        }

        // Get base directory for thumbnails
        $baseDirectory = $this->getCustomSubdirectory() ?? $this->getBaseDirectory();

        // Generate thumbnails
        $this->generateThumbnailsForFiles($filePaths, $baseDirectory);
    }

    /**
     * Delete thumbnails when files are removed
     */
    public function beforeDelete(): void
    {
        // Get the current field value (file paths)
        $filePaths = $this->getState();
        
        if (empty($filePaths)) {
            return;
        }

        // Ensure it's an array
        if (!is_array($filePaths)) {
            $filePaths = [$filePaths];
        }

        // Get base directory for thumbnails
        $baseDirectory = $this->getCustomSubdirectory() ?? $this->getBaseDirectory();

        // Delete thumbnails
        $this->deleteThumbnailsForFiles($filePaths, $baseDirectory);
    }

    /**
     * Configure thumbnail generation for gallery
     */
    public function galleryThumbnails(): static
    {
        return $this->autoGenerateThumbnails(true)
            ->convertToWebp(true)
            ->thumbnailSizes([
                'small' => ['width' => 150, 'height' => 150, 'quality' => 80, 'fit' => 'cover'],
                'medium' => ['width' => 300, 'height' => 300, 'quality' => 85, 'fit' => 'cover'],
                'large' => ['width' => 600, 'height' => 600, 'quality' => 90, 'fit' => 'cover'],
            ]);
    }

    /**
     * Configure thumbnail generation for products
     */
    public function productThumbnails(): static
    {
        return $this->autoGenerateThumbnails(true)
            ->convertToWebp(true)
            ->thumbnailSizes([
                'small' => ['width' => 100, 'height' => 100, 'quality' => 80, 'fit' => 'cover'],
                'medium' => ['width' => 250, 'height' => 250, 'quality' => 85, 'fit' => 'cover'],
                'large' => ['width' => 500, 'height' => 500, 'quality' => 90, 'fit' => 'cover'],
            ]);
    }

    /**
     * Configure thumbnail generation for avatars
     */
    public function avatarThumbnails(): static
    {
        return $this->autoGenerateThumbnails(true)
            ->convertToWebp(true)
            ->thumbnailSizes([
                'small' => ['width' => 50, 'height' => 50, 'quality' => 80, 'fit' => 'cover'],
                'medium' => ['width' => 100, 'height' => 100, 'quality' => 85, 'fit' => 'cover'],
                'large' => ['width' => 200, 'height' => 200, 'quality' => 90, 'fit' => 'cover'],
            ]);
    }
}
